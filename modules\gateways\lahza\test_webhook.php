<?php
/**
 * Lahza Webhook Test Tool
 * 
 * Test different webhook scenarios and payment statuses
 * Access: https://yourdomain.com/modules/gateways/lahza/test_webhook.php
 */

// Security check
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['adminid']) && !empty($_SESSION['adminid'])) {
    $isAdmin = true;
}

if (!$isAdmin && !isset($_GET['debug_key']) || (isset($_GET['debug_key']) && $_GET['debug_key'] !== 'lahza_debug_2025')) {
    die('Access denied. Admin login required or provide debug key.');
}

$action = $_POST['action'] ?? '';
$message = '';
$messageType = '';

// Sample webhook payloads for different scenarios
$webhookSamples = [
    'success' => [
        'event' => 'charge.success',
        'data' => [
            'id' => 'TXN_' . time(),
            'status' => 'success',
            'amount' => 5000, // $50.00 in cents
            'currency' => 'USD',
            'reference' => 'INV-123-' . time() . '-' . substr(md5(time()), 0, 8),
            'gateway_response' => 'Successful',
            'paid_at' => date('c'),
            'created_at' => date('c'),
            'channel' => 'card',
            'fees' => 150, // $1.50 in cents
            'metadata' => [
                'invoiceid' => '123',
                'clientid' => '456',
                'description' => 'Test Payment',
                'company' => 'Test Company'
            ],
            'customer' => [
                'id' => 456,
                'email' => '<EMAIL>',
                'phone' => '+************'
            ]
        ]
    ],
    'failed' => [
        'event' => 'charge.failed',
        'data' => [
            'id' => 'TXN_' . time(),
            'status' => 'failed',
            'amount' => 5000,
            'currency' => 'USD',
            'reference' => 'INV-123-' . time() . '-' . substr(md5(time()), 0, 8),
            'gateway_response' => 'Declined',
            'created_at' => date('c'),
            'channel' => 'card',
            'metadata' => [
                'invoiceid' => '123',
                'clientid' => '456',
                'description' => 'Test Payment',
                'company' => 'Test Company'
            ],
            'customer' => [
                'id' => 456,
                'email' => '<EMAIL>',
                'phone' => '+************'
            ]
        ]
    ],
    'pending' => [
        'event' => 'charge.pending',
        'data' => [
            'id' => 'TXN_' . time(),
            'status' => 'pending',
            'amount' => 5000,
            'currency' => 'USD',
            'reference' => 'INV-123-' . time() . '-' . substr(md5(time()), 0, 8),
            'gateway_response' => 'Pending',
            'created_at' => date('c'),
            'channel' => 'bank_transfer',
            'metadata' => [
                'invoiceid' => '123',
                'clientid' => '456',
                'description' => 'Test Payment',
                'company' => 'Test Company'
            ],
            'customer' => [
                'id' => 456,
                'email' => '<EMAIL>',
                'phone' => '+************'
            ]
        ]
    ]
];

// Handle webhook test
if ($action && isset($webhookSamples[$action])) {
    try {
        $webhookUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/modules/gateways/callback/lahza.php';
        $payload = json_encode($webhookSamples[$action]);
        
        // Get gateway settings for signature
        $gatewayParams = getGatewayVariables('lahza');
        $signature = hash_hmac('sha256', $payload, $gatewayParams['secretKey']);
        
        // Send webhook
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $webhookUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-Lahza-Signature: ' . $signature,
            'User-Agent: Lahza-Webhook-Test/1.0'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $message = "cURL Error: " . $error;
            $messageType = "error";
        } else {
            $message = "Webhook sent successfully! HTTP {$httpCode}. Response: " . $response;
            $messageType = ($httpCode == 200) ? "success" : "warning";
        }
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = "error";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Webhook Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .btn.warning { background: #ffc107; color: #212529; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .status-card { padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .status-card.success { border-left-color: #28a745; background: #f8fff9; }
        .status-card.failed { border-left-color: #dc3545; background: #fff8f8; }
        .status-card.pending { border-left-color: #ffc107; background: #fffef8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Lahza Webhook Test Tool</h1>
            <p>Test different payment statuses and webhook scenarios</p>
        </div>

        <?php if ($message): ?>
            <div class="test-section <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>📊 Payment Status Testing</h2>
            <p>Test how your webhook handles different payment statuses:</p>
            
            <div class="status-grid">
                <div class="status-card success">
                    <h3>✅ Successful Payment</h3>
                    <p>Tests successful payment processing and invoice marking as paid.</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="success">
                        <button type="submit" class="btn success">Test Success</button>
                    </form>
                </div>
                
                <div class="status-card failed">
                    <h3>❌ Failed Payment</h3>
                    <p>Tests failed payment handling and error logging.</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="failed">
                        <button type="submit" class="btn danger">Test Failed</button>
                    </form>
                </div>
                
                <div class="status-card pending">
                    <h3>⏳ Pending Payment</h3>
                    <p>Tests pending payment status (e.g., bank transfers).</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="pending">
                        <button type="submit" class="btn warning">Test Pending</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Supported Payment Statuses</h2>
            <div class="info">
                <h3>✅ Success Statuses (Invoice marked as paid):</h3>
                <ul>
                    <li><code>success</code> - Payment completed successfully</li>
                    <li><code>successful</code> - Alternative success status</li>
                    <li><code>completed</code> - Payment completed</li>
                    <li><code>paid</code> - Payment confirmed</li>
                </ul>
                
                <h3>❌ Failed Statuses (Payment failed):</h3>
                <ul>
                    <li><code>failed</code> - Payment failed</li>
                    <li><code>declined</code> - Payment declined by bank</li>
                    <li><code>cancelled</code> / <code>canceled</code> - Payment cancelled</li>
                    <li><code>abandoned</code> - Payment abandoned by user</li>
                </ul>
                
                <h3>⏳ Pending Statuses (Awaiting completion):</h3>
                <ul>
                    <li><code>pending</code> - Payment pending</li>
                    <li><code>processing</code> - Payment being processed</li>
                    <li><code>initiated</code> - Payment initiated</li>
                    <li><code>ongoing</code> - Payment in progress</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Webhook Event Types</h2>
            <div class="info">
                <h3>Supported Lahza Webhook Events:</h3>
                <ul>
                    <li><code>charge.success</code> - Successful payment</li>
                    <li><code>charge.failed</code> - Failed payment</li>
                    <li><code>refund.processed</code> - Refund completed</li>
                    <li><code>refund.failed</code> - Refund failed</li>
                    <li><code>refund.pending</code> - Refund pending</li>
                    <li><code>refund.processing</code> - Refund being processed</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 Sample Webhook Payloads</h2>
            
            <h3>Success Payload:</h3>
            <pre><?= htmlspecialchars(json_encode($webhookSamples['success'], JSON_PRETTY_PRINT)) ?></pre>
            
            <h3>Failed Payload:</h3>
            <pre><?= htmlspecialchars(json_encode($webhookSamples['failed'], JSON_PRETTY_PRINT)) ?></pre>
            
            <h3>Pending Payload:</h3>
            <pre><?= htmlspecialchars(json_encode($webhookSamples['pending'], JSON_PRETTY_PRINT)) ?></pre>
        </div>

        <div class="test-section warning">
            <h2>⚠️ Important Notes</h2>
            <ul>
                <li><strong>Invoice ID:</strong> Change the invoice ID in metadata to test with real invoices</li>
                <li><strong>Signature:</strong> Tests use your actual secret key for signature verification</li>
                <li><strong>Logs:</strong> Check WHMCS Gateway Logs to see webhook processing details</li>
                <li><strong>Security:</strong> Delete this file after testing</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔗 Related Tools</h2>
            <a href="view_logs.php?debug_key=lahza_debug_2025" target="_blank" class="btn">View Logs</a>
            <a href="test_gateway.php" target="_blank" class="btn">Gateway Test</a>
            <a href="../../../admin/logs.php?type=gateway" target="_blank" class="btn">WHMCS Gateway Logs</a>
        </div>
    </div>
</body>
</html>
