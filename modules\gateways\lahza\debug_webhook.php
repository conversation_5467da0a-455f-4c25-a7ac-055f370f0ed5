<?php
/**
 * <PERSON>hza Webhook Debug Tool
 * 
 * Debug webhook reception and processing
 * Access: https://yourdomain.com/modules/gateways/lahza/debug_webhook.php
 */

// Security check
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['adminid']) && !empty($_SESSION['adminid'])) {
    $isAdmin = true;
}

if (!$isAdmin && !isset($_GET['debug_key']) || (isset($_GET['debug_key']) && $_GET['debug_key'] !== 'lahza_debug_2025')) {
    die('Access denied. Admin login required or provide debug key.');
}

// Get webhook URL
$webhookUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/modules/gateways/callback/lahza.php';

// Test webhook accessibility
$webhookAccessible = false;
$webhookResponse = '';
$webhookHttpCode = 0;

try {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For testing
    
    $webhookResponse = curl_exec($ch);
    $webhookHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $webhookAccessible = ($webhookHttpCode > 0);
    curl_close($ch);
} catch (Exception $e) {
    $webhookResponse = 'Error: ' . $e->getMessage();
}

// Get gateway configuration
$gatewayParams = [];
try {
    $gatewayParams = getGatewayVariables('lahza');
} catch (Exception $e) {
    // Gateway not configured
}

// Get recent gateway logs
$recentLogs = [];
try {
    $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
    $stmt = $pdo->prepare("
        SELECT date, gateway, data, result 
        FROM tblgatewaylog 
        WHERE gateway LIKE '%lahza%' OR gateway LIKE '%Lahza%'
        ORDER BY date DESC 
        LIMIT 20
    ");
    $stmt->execute();
    $recentLogs = $stmt->fetchAll(\PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Handle error
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Webhook Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #6f42c1; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; max-height: 300px; overflow-y: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; font-size: 12px; }
        table th { background: #f8f9fa; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .status-card { padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .status-card.success { border-left-color: #28a745; background: #f8fff9; }
        .status-card.error { border-left-color: #dc3545; background: #fff8f8; }
        .status-card.warning { border-left-color: #ffc107; background: #fffef8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐛 Lahza Webhook Debug Tool</h1>
            <p>Debug webhook reception and invoice update issues</p>
        </div>

        <div class="status-grid">
            <div class="status-card <?= $webhookAccessible ? 'success' : 'error' ?>">
                <h3>🌐 Webhook Accessibility</h3>
                <p><strong>URL:</strong> <?= htmlspecialchars($webhookUrl) ?></p>
                <p><strong>Status:</strong> HTTP <?= $webhookHttpCode ?></p>
                <p><strong>Accessible:</strong> <?= $webhookAccessible ? 'Yes' : 'No' ?></p>
            </div>
            
            <div class="status-card <?= !empty($gatewayParams) ? 'success' : 'error' ?>">
                <h3>⚙️ Gateway Configuration</h3>
                <p><strong>Activated:</strong> <?= !empty($gatewayParams) ? 'Yes' : 'No' ?></p>
                <p><strong>Test Mode:</strong> <?= !empty($gatewayParams['testMode']) ? 'Yes' : 'No' ?></p>
                <p><strong>Public Key:</strong> <?= !empty($gatewayParams['publicKey']) ? 'Set' : 'Not Set' ?></p>
                <p><strong>Secret Key:</strong> <?= !empty($gatewayParams['secretKey']) ? 'Set' : 'Not Set' ?></p>
            </div>
            
            <div class="status-card <?= count($recentLogs) > 0 ? 'success' : 'warning' ?>">
                <h3>📊 Recent Activity</h3>
                <p><strong>Recent Logs:</strong> <?= count($recentLogs) ?> entries</p>
                <p><strong>Last Activity:</strong> <?= count($recentLogs) > 0 ? $recentLogs[0]['date'] : 'None' ?></p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Webhook URL Test</h2>
            <div class="info">
                <p><strong>Webhook URL:</strong> <code><?= htmlspecialchars($webhookUrl) ?></code></p>
                <p><strong>HTTP Response Code:</strong> <?= $webhookHttpCode ?></p>
                <p><strong>Response Preview:</strong></p>
                <pre><?= htmlspecialchars(substr($webhookResponse, 0, 500)) ?></pre>
            </div>
        </div>

        <?php if (!empty($gatewayParams)): ?>
        <div class="test-section">
            <h2>⚙️ Gateway Configuration Details</h2>
            <table>
                <tr><th>Setting</th><th>Value</th></tr>
                <tr><td>Gateway Name</td><td><?= htmlspecialchars($gatewayParams['name'] ?? 'N/A') ?></td></tr>
                <tr><td>Test Mode</td><td><?= !empty($gatewayParams['testMode']) ? 'Enabled' : 'Disabled' ?></td></tr>
                <tr><td>Public Key</td><td><?= !empty($gatewayParams['publicKey']) ? substr($gatewayParams['publicKey'], 0, 20) . '...' : 'Not Set' ?></td></tr>
                <tr><td>Secret Key</td><td><?= !empty($gatewayParams['secretKey']) ? 'Set (Hidden)' : 'Not Set' ?></td></tr>
                <tr><td>Default Currency</td><td><?= htmlspecialchars($gatewayParams['defaultCurrency'] ?? 'USD') ?></td></tr>
                <tr><td>Payment Channels</td><td><?= htmlspecialchars($gatewayParams['paymentChannels'] ?? 'N/A') ?></td></tr>
            </table>
        </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>📋 Recent Gateway Logs</h2>
            <?php if (count($recentLogs) > 0): ?>
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Gateway</th>
                            <th>Data</th>
                            <th>Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentLogs as $log): ?>
                            <tr>
                                <td><?= htmlspecialchars($log['date']) ?></td>
                                <td><?= htmlspecialchars($log['gateway']) ?></td>
                                <td><pre><?= htmlspecialchars(substr($log['data'], 0, 100)) ?><?= strlen($log['data']) > 100 ? '...' : '' ?></pre></td>
                                <td><?= htmlspecialchars($log['result']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="warning">
                    <p>No recent gateway logs found. This could indicate:</p>
                    <ul>
                        <li>No webhook requests have been received</li>
                        <li>Gateway is not properly configured</li>
                        <li>Webhook URL is not set in Lahza dashboard</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        <div class="test-section">
            <h2>🔧 Troubleshooting Steps</h2>
            <div class="info">
                <h3>If invoices are not updating automatically:</h3>
                <ol>
                    <li><strong>Check Webhook URL:</strong> Ensure it's accessible and returns HTTP 200</li>
                    <li><strong>Verify Lahza Dashboard:</strong> Confirm webhook URL is configured correctly</li>
                    <li><strong>Test Signature:</strong> Verify secret key matches between WHMCS and Lahza</li>
                    <li><strong>Check Logs:</strong> Look for webhook reception and processing errors</li>
                    <li><strong>Test Manually:</strong> Use webhook test tool to simulate payments</li>
                </ol>
                
                <h3>Common Issues:</h3>
                <ul>
                    <li><strong>SSL Certificate:</strong> Webhook URL must use valid HTTPS</li>
                    <li><strong>Firewall:</strong> Server must allow incoming webhook requests</li>
                    <li><strong>Signature Mismatch:</strong> Secret key must match exactly</li>
                    <li><strong>Invoice ID Extraction:</strong> Metadata must contain correct invoice ID</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🛠️ Debug Tools</h2>
            <a href="test_invoice_update.php?debug_key=lahza_debug_2025" class="btn">Test Invoice Update</a>
            <a href="test_webhook.php?debug_key=lahza_debug_2025" class="btn">Test Webhook</a>
            <a href="view_logs.php?debug_key=lahza_debug_2025" class="btn">View Detailed Logs</a>
            <a href="../../../admin/logs.php?type=gateway" target="_blank" class="btn">WHMCS Gateway Logs</a>
        </div>

        <div class="test-section warning">
            <h2>🚨 Quick Fixes</h2>
            <div class="warning">
                <h3>If webhook is not working:</h3>
                <ol>
                    <li><strong>Check Lahza Dashboard:</strong> Ensure webhook URL is set to:<br>
                        <code><?= htmlspecialchars($webhookUrl) ?></code></li>
                    <li><strong>Verify SSL:</strong> Test webhook URL in browser - should show error message</li>
                    <li><strong>Check Secret Key:</strong> Ensure it matches between WHMCS and Lahza</li>
                    <li><strong>Test Payment:</strong> Make a small test payment and check logs</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
