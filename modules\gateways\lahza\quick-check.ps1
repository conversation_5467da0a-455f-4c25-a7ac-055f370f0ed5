# Lahza Gateway Quick Check
# Simple PowerShell diagnostic for common issues
# Usage: .\quick-check.ps1 yourdomain.com

param(
    [Parameter(Position=0, Mandatory=$true)]
    [string]$Domain
)

Write-Host "🔍 Lahza Gateway Quick Check for: $Domain" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host ""

# Test 1: DNS Resolution
Write-Host "1. Testing DNS Resolution..." -ForegroundColor Yellow
try {
    $lahzaDns = Resolve-DnsName "api.lahza.io" -ErrorAction Stop
    Write-Host "   ✅ api.lahza.io → $($lahzaDns[0].IPAddress)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Cannot resolve api.lahza.io" -ForegroundColor Red
}

try {
    $domainDns = Resolve-DnsName $Domain -ErrorAction Stop
    Write-Host "   ✅ $Domain → $($domainDns[0].IPAddress)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Cannot resolve $Domain" -ForegroundColor Red
}

Write-Host ""

# Test 2: Basic Connectivity
Write-Host "2. Testing Connectivity..." -ForegroundColor Yellow

# Test Lahza API
try {
    $lahzaResponse = Invoke-WebRequest "https://api.lahza.io" -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "   ✅ Lahza API: HTTP $($lahzaResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Lahza API: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Webhook URL
$webhookUrl = "https://$Domain/modules/gateways/callback/lahza.php"
try {
    $webhookResponse = Invoke-WebRequest $webhookUrl -Method Head -TimeoutSec 10 -ErrorAction Stop
    Write-Host "   ✅ Webhook URL: HTTP $($webhookResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Webhook URL: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: SSL Certificate
Write-Host "3. Testing SSL Certificate..." -ForegroundColor Yellow
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect($Domain, 443)
    $sslStream = New-Object System.Net.Security.SslStream($tcpClient.GetStream())
    $sslStream.AuthenticateAsClient($Domain)
    $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($sslStream.RemoteCertificate)
    $tcpClient.Close()
    
    if ($cert.NotAfter -gt (Get-Date)) {
        Write-Host "   ✅ SSL Certificate valid until: $($cert.NotAfter)" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  SSL Certificate expired: $($cert.NotAfter)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ SSL Certificate: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Gateway Files
Write-Host "4. Testing Gateway Files..." -ForegroundColor Yellow

$files = @(
    "https://$Domain/modules/gateways/lahza.php",
    "https://$Domain/modules/gateways/callback/lahza.php"
)

foreach ($file in $files) {
    try {
        $fileResponse = Invoke-WebRequest $file -Method Head -TimeoutSec 5 -ErrorAction Stop
        $fileName = Split-Path $file -Leaf
        Write-Host "   ✅ $fileName: HTTP $($fileResponse.StatusCode)" -ForegroundColor Green
    } catch {
        $fileName = Split-Path $file -Leaf
        Write-Host "   ❌ $fileName: Not accessible" -ForegroundColor Red
    }
}

Write-Host ""

# Test 5: Diagnostic Tools
Write-Host "5. Testing Diagnostic Tools..." -ForegroundColor Yellow

$tools = @{
    "Tools Dashboard" = "https://$Domain/modules/gateways/lahza/tools.html"
    "Webhook Debug" = "https://$Domain/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025"
    "Gateway Test" = "https://$Domain/modules/gateways/lahza/test_gateway.php"
}

foreach ($tool in $tools.GetEnumerator()) {
    try {
        $toolResponse = Invoke-WebRequest $tool.Value -Method Head -TimeoutSec 5 -ErrorAction Stop
        Write-Host "   ✅ $($tool.Key): Available" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  $($tool.Key): Not accessible" -ForegroundColor Yellow
    }
}

Write-Host ""

# Summary
Write-Host "📋 QUICK SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 20 -ForegroundColor Cyan

Write-Host ""
Write-Host "🔗 Useful Links:" -ForegroundColor White
Write-Host "   • Tools Dashboard: https://$Domain/modules/gateways/lahza/tools.html" -ForegroundColor Gray
Write-Host "   • Webhook Debug: https://$Domain/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025" -ForegroundColor Gray
Write-Host "   • Invoice Test: https://$Domain/modules/gateways/lahza/test_invoice_update.php?debug_key=lahza_debug_2025" -ForegroundColor Gray

Write-Host ""
Write-Host "💡 Common Issues:" -ForegroundColor White
Write-Host "   • DNS issues → Contact hosting provider" -ForegroundColor Gray
Write-Host "   • Webhook not accessible → Check file permissions" -ForegroundColor Gray
Write-Host "   • SSL problems → Verify certificate validity" -ForegroundColor Gray
Write-Host "   • Invoice not updating → Check webhook configuration in Lahza dashboard" -ForegroundColor Gray

Write-Host ""
Write-Host "✅ For detailed diagnosis, run: .\diagnose.ps1 -Domain $Domain -TestWebhook" -ForegroundColor Green
