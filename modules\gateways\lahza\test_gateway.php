<?php
/**
 * Lahza Payment Gateway Test Script
 * 
 * This script helps test the Lahza payment gateway integration
 * Run this from your browser: https://yourdomain.com/modules/gateways/lahza/test_gateway.php
 */

// Prevent direct access
if (!defined("WHMCS")) {
    // Allow access for testing purposes
    define("WHMCS", true);
}

// Include WHMCS initialization
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/lib/LahzaApiHandler.php';

// HTML Header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Gateway Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Lahza Payment Gateway Test</h1>
        <p>This tool helps diagnose issues with your Lahza payment gateway integration.</p>

<?php

// Test 1: Check if gateway is configured
echo '<div class="test-section">';
echo '<h2>1. Gateway Configuration Test</h2>';

try {
    $gatewayParams = getGatewayVariables('lahza');
    
    if (!$gatewayParams || !isset($gatewayParams['type'])) {
        echo '<div class="error">❌ Gateway not found or not activated</div>';
        echo '<p>Please activate the Lahza gateway in WHMCS Admin → Setup → Payments → Payment Gateways</p>';
    } else {
        echo '<div class="success">✅ Gateway is activated</div>';
        
        // Check required settings
        $requiredSettings = ['publicKey', 'secretKey'];
        $missingSettings = [];
        
        foreach ($requiredSettings as $setting) {
            if (empty($gatewayParams[$setting])) {
                $missingSettings[] = $setting;
            }
        }
        
        if (!empty($missingSettings)) {
            echo '<div class="error">❌ Missing required settings: ' . implode(', ', $missingSettings) . '</div>';
        } else {
            echo '<div class="success">✅ All required settings are configured</div>';
        }
        
        // Show configuration (masked)
        echo '<h3>Current Configuration:</h3>';
        echo '<pre>';
        echo 'Test Mode: ' . ($gatewayParams['testMode'] ? 'Yes' : 'No') . "\n";
        echo 'Public Key: ' . (empty($gatewayParams['publicKey']) ? 'Not Set' : substr($gatewayParams['publicKey'], 0, 10) . '...') . "\n";
        echo 'Secret Key: ' . (empty($gatewayParams['secretKey']) ? 'Not Set' : 'Set (Hidden)') . "\n";
        echo 'Default Currency: ' . ($gatewayParams['defaultCurrency'] ?? 'USD') . "\n";
        echo '</pre>';
    }
} catch (Exception $e) {
    echo '<div class="error">❌ Error checking gateway configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

echo '</div>';

// Test 2: Network Connectivity Test
echo '<div class="test-section">';
echo '<h2>2. Network Connectivity Test</h2>';

// Test DNS resolution
$host = 'api.lahza.io';
$ip = gethostbyname($host);

if ($ip !== $host) {
    echo '<div class="success">✅ DNS Resolution successful: ' . htmlspecialchars($host) . ' → ' . htmlspecialchars($ip) . '</div>';
} else {
    echo '<div class="error">❌ DNS Resolution failed for: ' . htmlspecialchars($host) . '</div>';
    echo '<div class="warning">⚠️ This indicates network connectivity issues. Contact your hosting provider.</div>';
}

// Test basic HTTP connectivity
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_NOBODY, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo '<div class="error">❌ Connection Error: ' . htmlspecialchars($error) . '</div>';
    if (strpos($error, 'Could not resolve host') !== false) {
        echo '<div class="warning">⚠️ DNS resolution issue. Check your network settings.</div>';
    }
} else {
    echo '<div class="success">✅ HTTP Connection successful (HTTP ' . $httpCode . ')</div>';
}

echo '<p><a href="test_connection.php" target="_blank" class="btn">Run Detailed Connection Test</a></p>';
echo '</div>';

// Test 3: API Connection Test
if (isset($gatewayParams) && !empty($gatewayParams['publicKey']) && !empty($gatewayParams['secretKey'])) {
    echo '<div class="test-section">';
    echo '<h2>3. API Connection Test</h2>';

    try {
        $lahzaApi = new LahzaApiHandler(
            $gatewayParams['publicKey'],
            $gatewayParams['secretKey'],
            $gatewayParams['testMode']
        );

        echo '<div class="info">ℹ️ API Handler initialized successfully</div>';
        echo '<p>API Base URL: https://api.lahza.io</p>';
        echo '<p>Test Mode: ' . ($gatewayParams['testMode'] ? 'Enabled' : 'Disabled') . '</p>';

        // Test basic connectivity
        echo '<div class="warning">⚠️ Full API connectivity test requires valid API keys and a test transaction</div>';

    } catch (Exception $e) {
        echo '<div class="error">❌ API initialization failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }

    echo '</div>';
}

// Test 4: Webhook URL Test
echo '<div class="test-section">';
echo '<h2>4. Webhook URL Test</h2>';

$webhookUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/modules/gateways/callback/lahza.php';
echo '<p><strong>Webhook URL:</strong> <code>' . htmlspecialchars($webhookUrl) . '</code></p>';

// Test if webhook file exists
$webhookFile = __DIR__ . '/../callback/lahza.php';
if (file_exists($webhookFile)) {
    echo '<div class="success">✅ Webhook file exists</div>';
} else {
    echo '<div class="error">❌ Webhook file not found</div>';
}

// Test webhook accessibility
echo '<p><a href="' . htmlspecialchars($webhookUrl) . '" target="_blank" class="btn">Test Webhook URL</a></p>';
echo '<p><small>The webhook should return an error message when accessed directly (this is normal)</small></p>';

echo '</div>';

// Test 5: Database Connection Test
echo '<div class="test-section">';
echo '<h2>5. Database Connection Test</h2>';

try {
    $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
    echo '<div class="success">✅ Database connection successful</div>';
    
    // Test gateway log table
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tblgatewaylog WHERE gateway = 'Lahza Payment Gateway' ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $logCount = $stmt->fetch();
    
    echo '<p>Gateway log entries: ' . ($logCount['count'] ?? 0) . '</p>';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

echo '</div>';

// Test 6: File Permissions Test
echo '<div class="test-section">';
echo '<h2>6. File Permissions Test</h2>';

$filesToCheck = [
    __DIR__ . '/../lahza.php' => 'Main gateway file',
    __DIR__ . '/../callback/lahza.php' => 'Callback file',
    __DIR__ . '/lib/LahzaApiHandler.php' => 'API handler'
];

foreach ($filesToCheck as $file => $description) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo '<div class="success">✅ ' . $description . ' is readable</div>';
        } else {
            echo '<div class="error">❌ ' . $description . ' is not readable</div>';
        }
    } else {
        echo '<div class="error">❌ ' . $description . ' not found</div>';
    }
}

echo '</div>';

?>

        <div class="test-section info">
            <h2>📋 Next Steps</h2>
            <ul>
                <li>If all tests pass, try making a test payment</li>
                <li>Check WHMCS Gateway Logs: <strong>Utilities → Logs → Gateway Log</strong></li>
                <li>Enable test mode for safe testing</li>
                <li>Configure webhook URL in your Lahza dashboard</li>
                <li>Test with small amounts first</li>
            </ul>
        </div>

        <div class="test-section warning">
            <h2>🔒 Security Note</h2>
            <p><strong>Important:</strong> Delete this test file after testing for security reasons.</p>
            <p>This file exposes configuration information and should not be accessible in production.</p>
        </div>

    </div>
</body>
</html>
