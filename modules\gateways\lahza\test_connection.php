<?php
/**
 * Lahza API Connection Test
 * 
 * Test basic connectivity to Lahza API
 * Access: https://yourdomain.com/modules/gateways/lahza/test_connection.php
 */

// Prevent direct access in production
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza API Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Lahza API Connection Test</h1>
        <p>Testing connectivity to Lahza API endpoints...</p>

        <?php
        
        // Test 1: DNS Resolution
        echo '<h2>1. DNS Resolution Test</h2>';
        $host = 'api.lahza.io';
        $ip = gethostbyname($host);
        
        if ($ip !== $host) {
            echo '<div class="test-result success">✅ DNS Resolution successful: ' . htmlspecialchars($host) . ' → ' . htmlspecialchars($ip) . '</div>';
        } else {
            echo '<div class="test-result error">❌ DNS Resolution failed for: ' . htmlspecialchars($host) . '</div>';
            echo '<div class="test-result info">This could indicate network connectivity issues or DNS server problems.</div>';
        }
        
        // Test 2: Basic HTTP Connectivity
        echo '<h2>2. HTTP Connectivity Test</h2>';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_USERAGENT, 'WHMCS-Lahza-Test/1.0');
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
        curl_setopt($ch, CURLOPT_HEADER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);
        
        if ($error) {
            echo '<div class="test-result error">❌ cURL Error: ' . htmlspecialchars($error) . '</div>';
            echo '<div class="test-result info">Common causes: Firewall blocking, SSL certificate issues, or network connectivity problems.</div>';
        } else {
            echo '<div class="test-result success">✅ HTTP Connection successful (HTTP ' . $httpCode . ')</div>';
            echo '<div class="test-result info">Connection time: ' . round($info['connect_time'], 3) . 's, Total time: ' . round($info['total_time'], 3) . 's</div>';
        }
        
        // Test 3: API Endpoint Test
        echo '<h2>3. API Endpoint Test</h2>';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io/transaction/initialize');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_USERAGENT, 'WHMCS-Lahza-Test/1.0');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array('test' => 'connection')));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Accept: application/json'
        ));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);
        
        if ($error) {
            echo '<div class="test-result error">❌ API Endpoint Error: ' . htmlspecialchars($error) . '</div>';
        } else {
            if ($httpCode == 401 || $httpCode == 400) {
                echo '<div class="test-result success">✅ API Endpoint reachable (HTTP ' . $httpCode . ' - Expected for unauthorized request)</div>';
            } else {
                echo '<div class="test-result warning">⚠️ API Endpoint responded with HTTP ' . $httpCode . '</div>';
            }
            
            if ($response) {
                echo '<div class="test-result info">Response preview:</div>';
                echo '<pre>' . htmlspecialchars(substr($response, 0, 500)) . '</pre>';
            }
        }
        
        // Test 4: SSL Certificate Test
        echo '<h2>4. SSL Certificate Test</h2>';
        
        $context = stream_context_create([
            "ssl" => [
                "capture_peer_cert" => true,
                "verify_peer" => true,
                "verify_peer_name" => true,
            ],
        ]);
        
        $stream = @stream_socket_client(
            "ssl://api.lahza.io:443",
            $errno,
            $errstr,
            10,
            STREAM_CLIENT_CONNECT,
            $context
        );
        
        if ($stream) {
            $params = stream_context_get_params($stream);
            $cert = $params['options']['ssl']['peer_certificate'];
            $certinfo = openssl_x509_parse($cert);
            
            echo '<div class="test-result success">✅ SSL Certificate valid</div>';
            echo '<div class="test-result info">Certificate issued to: ' . htmlspecialchars($certinfo['subject']['CN'] ?? 'Unknown') . '</div>';
            echo '<div class="test-result info">Valid until: ' . date('Y-m-d H:i:s', $certinfo['validTo_time_t']) . '</div>';
            
            fclose($stream);
        } else {
            echo '<div class="test-result error">❌ SSL Certificate validation failed: ' . htmlspecialchars($errstr) . '</div>';
        }
        
        // Test 5: Network Information
        echo '<h2>5. Network Information</h2>';
        
        echo '<div class="test-result info">';
        echo '<strong>Server Information:</strong><br>';
        echo 'Server IP: ' . ($_SERVER['SERVER_ADDR'] ?? 'Unknown') . '<br>';
        echo 'User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . '<br>';
        echo 'PHP Version: ' . PHP_VERSION . '<br>';
        echo 'cURL Version: ' . (function_exists('curl_version') ? curl_version()['version'] : 'Not available') . '<br>';
        echo 'OpenSSL Version: ' . OPENSSL_VERSION_TEXT . '<br>';
        echo '</div>';
        
        // Test 6: Recommendations
        echo '<h2>6. Recommendations</h2>';
        
        if ($ip === $host) {
            echo '<div class="test-result warning">⚠️ DNS resolution failed. Check your DNS settings or contact your hosting provider.</div>';
        }
        
        if ($error && strpos($error, 'Could not resolve host') !== false) {
            echo '<div class="test-result warning">⚠️ Host resolution error. This could be due to:</div>';
            echo '<ul>';
            echo '<li>DNS server issues</li>';
            echo '<li>Firewall blocking DNS queries</li>';
            echo '<li>Network connectivity problems</li>';
            echo '<li>Incorrect DNS configuration</li>';
            echo '</ul>';
        }
        
        if ($error && strpos($error, 'SSL') !== false) {
            echo '<div class="test-result warning">⚠️ SSL-related error. Try:</div>';
            echo '<ul>';
            echo '<li>Updating your SSL certificates</li>';
            echo '<li>Checking OpenSSL version</li>';
            echo '<li>Contacting your hosting provider</li>';
            echo '</ul>';
        }
        
        echo '<div class="test-result info">';
        echo '<strong>Next Steps:</strong><br>';
        echo '1. If all tests pass, your connection to Lahza API is working<br>';
        echo '2. If tests fail, contact your hosting provider or system administrator<br>';
        echo '3. For API-specific issues, check your Lahza API credentials<br>';
        echo '4. Run the full gateway test: <a href="test_gateway.php">test_gateway.php</a>';
        echo '</div>';
        
        ?>
        
        <div style="margin-top: 20px;">
            <a href="test_gateway.php" class="btn">Run Full Gateway Test</a>
            <a href="javascript:location.reload()" class="btn">Refresh Test</a>
        </div>
        
        <div class="test-result warning" style="margin-top: 20px;">
            <strong>Security Note:</strong> Delete this test file after troubleshooting for security reasons.
        </div>
    </div>
</body>
</html>
