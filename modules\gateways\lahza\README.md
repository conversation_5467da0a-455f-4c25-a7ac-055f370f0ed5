# Lahza Payment Gateway for WHMCS - FIXED VERSION

**Developed by WIDDX (https://widdx.com)**
**© 2025 All rights reserved.**

## 🔧 RECENT FIXES APPLIED

**Version 1.1.0 - Bug Fixes & Improvements:**
- ✅ Fixed client ID extraction from WHMCS parameters (supports multiple ID formats)
- ✅ Improved phone number validation and cleaning with better defaults
- ✅ Enhanced webhook signature verification with multiple header support
- ✅ Better error handling and logging throughout the system
- ✅ Improved API response parsing with multiple fallback options
- ✅ Enhanced callback data extraction with multiple structure support
- ✅ Added comprehensive debugging and logging features
- ✅ Fixed API endpoint selection for test vs production modes
- ✅ Improved transaction reference generation and validation
- ✅ Added test script for easy troubleshooting

## Overview

The Lahza Payment Gateway module for WHMCS provides seamless integration with the Lahza payment platform, enabling businesses to accept payments through multiple payment methods including credit cards, bank transfers, and mobile money solutions.

## Features

- ✅ **Multiple Payment Methods**: Support for cards, bank transfers, USSD, QR codes, mobile money, and bank transfers
- ✅ **Multi-Currency Support**: USD, ILS (Israeli Shekel), and JOD (Jordanian Dinar)
- ✅ **Webhook Integration**: Real-time payment notifications
- ✅ **Refund Support**: Process refunds directly from WHMCS admin
- ✅ **Test Mode**: Safe testing environment
- ✅ **Secure Transactions**: HMAC signature verification for webhooks
- ✅ **PSR-12 Compliant**: Clean, maintainable code following PHP standards
- ✅ **Comprehensive Logging**: Detailed transaction logging for debugging
- ✅ **Error Handling**: Robust error handling and user feedback

## Requirements

- **WHMCS**: Version 7.0 or higher
- **PHP**: Version 7.0 or higher
- **PHP Extensions**: cURL, JSON, OpenSSL
- **SSL Certificate**: Required for webhook endpoints
- **Lahza Account**: Active Lahza merchant account

## Installation

1. **Download the Module**
   - Extract the module files to your WHMCS installation directory
   - Ensure the following structure exists:
     ```
     modules/gateways/
     ├── lahza.php
     ├── lahza/
     │   ├── lib/
     │   │   └── LahzaApiHandler.php
     │   ├── whmcs.json
     │   ├── logo.png
     │   └── README.md
     └── callback/
         └── lahza.php
     ```

2. **Activate the Gateway**
   - Log in to your WHMCS Admin Area
   - Navigate to **Setup > Payments > Payment Gateways**
   - Find "Lahza Payment Gateway" in the list
   - Click **Activate**

3. **Configure the Gateway**
   - Enter your Lahza **Public Key**
   - Enter your Lahza **Secret Key**
   - Select your preferred **Default Currency**
   - Configure **Payment Channels** (comma-separated list)
   - Enable **Test Mode** for testing (disable for production)

## Configuration Options

### Required Settings

| Setting | Description |
|---------|-------------|
| **Public Key** | Your Lahza public key (use test key for testing) |
| **Secret Key** | Your Lahza secret key (use test key for testing) |

### Optional Settings

| Setting | Description | Default |
|---------|-------------|---------|
| **Test Mode** | Enable/disable test mode | Disabled |
| **Default Currency** | Default currency for transactions | USD |
| **Payment Channels** | Comma-separated list of payment methods | card,bank,mobile_money |

### Payment Channels

Available payment channels:
- `card` - Credit/Debit Cards
- `bank` - Bank Transfers
- `ussd` - USSD Payments
- `qr` - QR Code Payments
- `mobile_money` - Mobile Money
- `bank_transfer` - Direct Bank Transfers

## Webhook Configuration

1. **Set Up Webhook URL**
   - In your Lahza dashboard, configure the webhook URL:
   - `https://yourdomain.com/modules/gateways/callback/lahza.php`
   - Replace `yourdomain.com` with your actual domain

2. **Webhook Events**
   - The module handles the following webhook events:
     - `charge.success` - Successful payment
     - `charge.failed` - Failed payment
     - `refund.success` - Successful refund
     - `refund.failed` - Failed refund

3. **Security**
   - Webhooks are secured using HMAC SHA256 signatures
   - The module automatically verifies webhook authenticity

## Testing

### Test Mode Setup

1. Enable **Test Mode** in the gateway configuration
2. Use your Lahza test API keys
3. Configure test webhook URL in Lahza dashboard
4. Process test transactions to verify functionality

### Test Cards

Use Lahza's test card numbers for testing different scenarios:
- Successful payments
- Failed payments
- Different card types

## Troubleshooting

### 🔧 Quick Test Script

**NEW:** Use the built-in test script to quickly diagnose issues:
```
https://yourdomain.com/modules/gateways/lahza/test_gateway.php
```
This script will check:
- Gateway configuration
- API connectivity
- Webhook URL accessibility
- Database connections
- File permissions

**⚠️ Important:** Delete the test file after testing for security reasons.

### Common Issues & Solutions

1. **"Module Not Activated" Error**
   - ✅ Ensure the gateway is activated in WHMCS Admin
   - ✅ Check file permissions (files should be readable by web server)
   - ✅ Verify all required files are present

2. **Payment Initialization Failed**
   - ✅ Verify API keys are correct and match the test/live mode
   - ✅ Check if client has valid email and phone number
   - ✅ Review gateway logs for detailed error messages
   - ✅ Ensure amount is greater than 0
   - ✅ Verify currency is supported (USD, ILS, JOD)

3. **Webhook Not Working (Payment successful but invoice not marked as paid)**
   - ✅ Ensure webhook URL is publicly accessible via HTTPS
   - ✅ Verify SSL certificate is valid and trusted
   - ✅ Check webhook URL configuration in Lahza dashboard
   - ✅ Review webhook signature verification
   - ✅ Check server logs for webhook errors

4. **Client ID Issues (Fixed in v1.1.0)**
   - ✅ The gateway now supports multiple client ID formats
   - ✅ Automatic fallback if client ID is not found
   - ✅ Enhanced logging for client data debugging

5. **Phone Number Validation Issues (Fixed in v1.1.0)**
   - ✅ Improved phone number cleaning and validation
   - ✅ Better default phone numbers for testing
   - ✅ Enhanced error messages for phone validation

### Enhanced Debugging (New Features)

1. **Comprehensive Logging**
   - Navigate to **Utilities > Logs > Gateway Log**
   - Look for "Lahza Payment Gateway" entries
   - New detailed logging includes API responses and data structures

2. **Response Structure Analysis**
   - The gateway now logs complete API response structures
   - Multiple fallback options for finding authorization URLs
   - Better error message extraction from API responses

3. **Webhook Debugging**
   - Enhanced webhook signature verification with multiple header support
   - Detailed logging of all webhook headers and data
   - Support for test mode webhook processing

4. **System Health Check**
   - Use the test script to verify all components
   - Check database connectivity
   - Verify file permissions and accessibility

## API Reference

### LahzaApiHandler Class

The module includes a comprehensive API handler class with the following methods:

- `initializeTransaction($transactionData)` - Initialize a new transaction
- `verifyTransaction($reference)` - Verify transaction status
- `processRefund($refundData)` - Process a refund
- `cancelSubscription($subscriptionId)` - Cancel a subscription
- `getTransaction($transactionId)` - Get transaction details

## Security Considerations

1. **API Keys**
   - Store API keys securely
   - Use test keys for development/testing
   - Never expose secret keys in client-side code

2. **Webhook Security**
   - All webhooks are verified using HMAC signatures
   - Use HTTPS for all webhook endpoints
   - Validate all incoming data

3. **Data Sanitization**
   - All user input is sanitized and validated
   - SQL injection protection through WHMCS functions
   - XSS prevention through proper escaping

## Support

### Documentation
- **Lahza API Documentation**: https://api-docs.lahza.io/
- **Lahza Integration Guide**: https://docs.lahza.io/
- **WHMCS Developer Docs**: https://developers.whmcs.com/

### Technical Support
- **WIDDX Support**: https://widdx.com/support
- **Email**: <EMAIL>
- **Lahza Support**: Contact Lahza directly for API-related issues

## Changelog

### Version 1.1.0 (2025-01-16) - Bug Fixes & Improvements
- 🔧 **Fixed client ID extraction** - Now supports multiple ID formats from WHMCS
- 🔧 **Improved phone number validation** - Better cleaning and default values
- 🔧 **Enhanced webhook processing** - Multiple header support for signature verification
- 🔧 **Better error handling** - More detailed error messages and logging
- 🔧 **API response parsing** - Multiple fallback options for authorization URLs
- 🔧 **Callback data extraction** - Support for multiple webhook data structures
- 🔧 **Test/Production API endpoints** - Proper endpoint selection based on mode
- 🔧 **Transaction reference generation** - Improved unique reference creation
- ✨ **Added test script** - Built-in diagnostic tool for troubleshooting
- ✨ **Enhanced logging** - Comprehensive debugging information
- ✨ **Response structure analysis** - Better API response debugging
- 📚 **Updated documentation** - Improved troubleshooting guide

### Version 1.0.0 (2025-01-15)
- Initial release
- Support for Lahza payment gateway
- Multiple payment methods support
- Webhook integration
- Refund functionality
- Test mode support
- Multi-currency support (USD, ILS, JOD)
- Comprehensive error handling and logging
- PSR-12 compliant code

## License

This module is proprietary software developed by WIDDX. All rights reserved.

## Contributing

For feature requests or bug reports, please contact WIDDX support.

---

**Developed with ❤️ by WIDDX**  
**Making payment integration simple and secure.**