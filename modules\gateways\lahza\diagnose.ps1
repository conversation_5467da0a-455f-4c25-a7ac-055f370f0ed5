# Lahza Payment Gateway Diagnostic Tool
# PowerShell script for comprehensive gateway diagnosis
# Usage: .\diagnose.ps1 -Domain "yourdomain.com" [-Verbose]

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$false)]
    [string]$InvoiceId = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$TestWebhook,
    
    [Parameter(Mandatory=$false)]
    [switch]$CheckLogs
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"
$White = "White"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Symbol = ""
    )
    
    if ($Symbol) {
        Write-Host "$Symbol " -ForegroundColor $Color -NoNewline
    }
    Write-Host $Message -ForegroundColor $Color
}

# Function to test URL accessibility
function Test-URLAccessibility {
    param([string]$Url)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method Head -TimeoutSec 10 -ErrorAction Stop
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            StatusDescription = $response.StatusDescription
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = 0
            StatusDescription = "Failed"
            Error = $_.Exception.Message
        }
    }
}

# Function to test DNS resolution
function Test-DNSResolution {
    param([string]$Hostname)
    
    try {
        $result = Resolve-DnsName -Name $Hostname -ErrorAction Stop
        return @{
            Success = $true
            IPAddress = $result[0].IPAddress
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            IPAddress = $null
            Error = $_.Exception.Message
        }
    }
}

# Function to test webhook with sample data
function Test-WebhookEndpoint {
    param(
        [string]$WebhookUrl,
        [string]$InvoiceId = "123"
    )
    
    $samplePayload = @{
        event = "charge.success"
        data = @{
            id = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')"
            status = "success"
            amount = 5000
            currency = "USD"
            reference = "INV-$InvoiceId-$(Get-Date -Format 'yyyyMMddHHmmss')"
            metadata = @{
                invoiceid = $InvoiceId
                clientid = "1"
                description = "Test Payment"
            }
        }
    } | ConvertTo-Json -Depth 3
    
    try {
        $headers = @{
            'Content-Type' = 'application/json'
            'X-Lahza-Signature' = 'test_signature'
            'User-Agent' = 'Lahza-PowerShell-Diagnostic/1.0'
        }
        
        $response = Invoke-WebRequest -Uri $WebhookUrl -Method Post -Body $samplePayload -Headers $headers -TimeoutSec 15 -ErrorAction Stop
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Response = $response.Content
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = 0
            Response = $null
            Error = $_.Exception.Message
        }
    }
}

# Function to check SSL certificate
function Test-SSLCertificate {
    param([string]$Hostname)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.Connect($Hostname, 443)
        
        $sslStream = New-Object System.Net.Security.SslStream($tcpClient.GetStream())
        $sslStream.AuthenticateAsClient($Hostname)
        
        $cert = $sslStream.RemoteCertificate
        $cert2 = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($cert)
        
        $tcpClient.Close()
        
        return @{
            Success = $true
            Subject = $cert2.Subject
            Issuer = $cert2.Issuer
            NotAfter = $cert2.NotAfter
            Valid = $cert2.NotAfter -gt (Get-Date)
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            Subject = $null
            Issuer = $null
            NotAfter = $null
            Valid = $false
            Error = $_.Exception.Message
        }
    }
}

# Main diagnostic function
function Start-LahzaDiagnostic {
    Write-ColorOutput "🔍 Lahza Payment Gateway Diagnostic Tool" $Blue "="
    Write-ColorOutput "Domain: $Domain" $White
    Write-ColorOutput "Time: $(Get-Date)" $White
    Write-Host ""
    
    # Test 1: DNS Resolution for Lahza API
    Write-ColorOutput "1. Testing DNS Resolution for Lahza API..." $Blue "🌐"
    $dnsTest = Test-DNSResolution -Hostname "api.lahza.io"
    
    if ($dnsTest.Success) {
        Write-ColorOutput "✅ DNS Resolution successful: api.lahza.io → $($dnsTest.IPAddress)" $Green
    } else {
        Write-ColorOutput "❌ DNS Resolution failed: $($dnsTest.Error)" $Red
    }
    Write-Host ""
    
    # Test 2: Lahza API Connectivity
    Write-ColorOutput "2. Testing Lahza API Connectivity..." $Blue "🔗"
    $apiTest = Test-URLAccessibility -Url "https://api.lahza.io"
    
    if ($apiTest.Success) {
        Write-ColorOutput "✅ Lahza API accessible: HTTP $($apiTest.StatusCode)" $Green
    } else {
        Write-ColorOutput "❌ Lahza API not accessible: $($apiTest.Error)" $Red
    }
    Write-Host ""
    
    # Test 3: Your Domain DNS Resolution
    Write-ColorOutput "3. Testing Your Domain DNS Resolution..." $Blue "🌐"
    $domainDnsTest = Test-DNSResolution -Hostname $Domain
    
    if ($domainDnsTest.Success) {
        Write-ColorOutput "✅ Domain DNS Resolution successful: $Domain → $($domainDnsTest.IPAddress)" $Green
    } else {
        Write-ColorOutput "❌ Domain DNS Resolution failed: $($domainDnsTest.Error)" $Red
    }
    Write-Host ""
    
    # Test 4: SSL Certificate Check
    Write-ColorOutput "4. Testing SSL Certificate..." $Blue "🔒"
    $sslTest = Test-SSLCertificate -Hostname $Domain
    
    if ($sslTest.Success) {
        Write-ColorOutput "✅ SSL Certificate valid" $Green
        Write-ColorOutput "   Subject: $($sslTest.Subject)" $White
        Write-ColorOutput "   Expires: $($sslTest.NotAfter)" $White
        
        if (-not $sslTest.Valid) {
            Write-ColorOutput "⚠️  Certificate is expired!" $Yellow
        }
    } else {
        Write-ColorOutput "❌ SSL Certificate check failed: $($sslTest.Error)" $Red
    }
    Write-Host ""
    
    # Test 5: Webhook URL Accessibility
    $webhookUrl = "https://$Domain/modules/gateways/callback/lahza.php"
    Write-ColorOutput "5. Testing Webhook URL Accessibility..." $Blue "📡"
    Write-ColorOutput "   URL: $webhookUrl" $White
    
    $webhookTest = Test-URLAccessibility -Url $webhookUrl
    
    if ($webhookTest.Success) {
        Write-ColorOutput "✅ Webhook URL accessible: HTTP $($webhookTest.StatusCode)" $Green
    } else {
        Write-ColorOutput "❌ Webhook URL not accessible: $($webhookTest.Error)" $Red
    }
    Write-Host ""
    
    # Test 6: Gateway Files Check
    Write-ColorOutput "6. Testing Gateway Files..." $Blue "📁"
    
    $gatewayFiles = @(
        "https://$Domain/modules/gateways/lahza.php",
        "https://$Domain/modules/gateways/callback/lahza.php"
    )
    
    foreach ($file in $gatewayFiles) {
        $fileTest = Test-URLAccessibility -Url $file
        $fileName = Split-Path $file -Leaf
        
        if ($fileTest.Success) {
            Write-ColorOutput "✅ $fileName accessible" $Green
        } else {
            Write-ColorOutput "❌ $fileName not accessible" $Red
        }
    }
    Write-Host ""
    
    # Test 7: Diagnostic Tools Check
    Write-ColorOutput "7. Testing Diagnostic Tools..." $Blue "🛠️"
    
    $diagnosticTools = @(
        "https://$Domain/modules/gateways/lahza/tools.html",
        "https://$Domain/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025",
        "https://$Domain/modules/gateways/lahza/test_gateway.php"
    )
    
    foreach ($tool in $diagnosticTools) {
        $toolTest = Test-URLAccessibility -Url $tool
        $toolName = Split-Path $tool -Leaf
        
        if ($toolTest.Success) {
            Write-ColorOutput "✅ $toolName accessible" $Green
        } else {
            Write-ColorOutput "⚠️  $toolName not accessible (may be normal)" $Yellow
        }
    }
    Write-Host ""
    
    # Test 8: Webhook Functionality (if requested)
    if ($TestWebhook) {
        Write-ColorOutput "8. Testing Webhook Functionality..." $Blue "🧪"
        
        $webhookFuncTest = Test-WebhookEndpoint -WebhookUrl $webhookUrl -InvoiceId $InvoiceId
        
        if ($webhookFuncTest.Success) {
            Write-ColorOutput "✅ Webhook responds: HTTP $($webhookFuncTest.StatusCode)" $Green
            if ($Verbose) {
                Write-ColorOutput "   Response: $($webhookFuncTest.Response)" $White
            }
        } else {
            Write-ColorOutput "❌ Webhook test failed: $($webhookFuncTest.Error)" $Red
        }
        Write-Host ""
    }
    
    # Summary and Recommendations
    Write-ColorOutput "📋 DIAGNOSTIC SUMMARY" $Blue "="
    Write-Host ""
    
    $issues = @()
    $warnings = @()
    
    if (-not $dnsTest.Success) { $issues += "DNS resolution for api.lahza.io failed" }
    if (-not $apiTest.Success) { $issues += "Cannot connect to Lahza API" }
    if (-not $domainDnsTest.Success) { $issues += "Your domain DNS resolution failed" }
    if (-not $sslTest.Success) { $issues += "SSL certificate issues" }
    if (-not $sslTest.Valid) { $warnings += "SSL certificate is expired" }
    if (-not $webhookTest.Success) { $issues += "Webhook URL not accessible" }
    
    if ($issues.Count -eq 0) {
        Write-ColorOutput "🎉 All critical tests passed!" $Green
        Write-ColorOutput "Your Lahza gateway appears to be configured correctly." $Green
    } else {
        Write-ColorOutput "🚨 Issues found:" $Red
        foreach ($issue in $issues) {
            Write-ColorOutput "   • $issue" $Red
        }
    }
    
    if ($warnings.Count -gt 0) {
        Write-ColorOutput "⚠️  Warnings:" $Yellow
        foreach ($warning in $warnings) {
            Write-ColorOutput "   • $warning" $Yellow
        }
    }
    
    Write-Host ""
    Write-ColorOutput "🔗 Next Steps:" $Blue
    Write-ColorOutput "1. Open diagnostic tools: https://$Domain/modules/gateways/lahza/tools.html" $White
    Write-ColorOutput "2. Test webhook: https://$Domain/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025" $White
    Write-ColorOutput "3. Check logs: https://$Domain/modules/gateways/lahza/view_logs.php?debug_key=lahza_debug_2025" $White
    
    if ($issues.Count -gt 0) {
        Write-Host ""
        Write-ColorOutput "📞 Need Help?" $Blue
        Write-ColorOutput "Contact your hosting provider with this diagnostic report." $White
        Write-ColorOutput "Common request: 'Enable outbound HTTPS connections and whitelist api.lahza.io'" $White
    }
}

# Run the diagnostic
try {
    Start-LahzaDiagnostic
}
catch {
    Write-ColorOutput "❌ Diagnostic failed: $($_.Exception.Message)" $Red
    Write-ColorOutput "Please check your parameters and try again." $Yellow
}

Write-Host ""
Write-ColorOutput "Diagnostic completed at $(Get-Date)" $Blue
