<?php
/**
 * Developed by WIDDX (https://widdx.com)
 * © 2025 All Rights Reserved
 * 
 * Lahza Payment Gateway for WHMCS
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/lahza/lib/LahzaApiHandler.php';

// Import Capsule for database access
use WHMCS\Database\Capsule;

/**
 * Define module related meta data.
 *
 * Values returned here are used to determine module related capabilities and
 * settings.
 *
 * @see https://developers.whmcs.com/payment-gateways/meta-data-params/
 *
 * @return array
 */
function lahza_MetaData()
{
    return array(
        'DisplayName' => 'Lahza Payment Gateway',
        'APIVersion' => '1.1',
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
    );
}

/**
 * Define gateway configuration options.
 *
 * The fields you define here determine the configuration options that are
 * presented to administrator users when activating and configuring your
 * payment gateway module for use.
 *
 * @return array
 */
function lahza_config()
{
    return array(
        // the friendly display name for a payment gateway should be
        // defined here for backwards compatibility
        'FriendlyName' => array(
            'Type' => 'System',
            'Value' => 'Lahza Payment Gateway',
        ),
        // Public Key
        'publicKey' => array(
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Lahza public key here (use test key for test mode)',
        ),
        // Secret Key
        'secretKey' => array(
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Lahza secret key here (use test key for test mode)',
        ),
        // Test Mode
        'testMode' => array(
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Tick to enable test mode',
        ),
        // Default Currency
        'defaultCurrency' => array(
            'FriendlyName' => 'Default Currency',
            'Type' => 'dropdown',
            'Options' => array(
                'ILS' => 'Israeli Shekel (ILS)',
                'USD' => 'US Dollar (USD)',
                'JOD' => 'Jordanian Dinar (JOD)',
            ),
            'Default' => 'USD',
            'Description' => 'Default currency for transactions',
        ),
        // Payment Channels
        'paymentChannels' => array(
            'FriendlyName' => 'Payment Channels',
            'Type' => 'text',
            'Size' => '50',
            'Default' => 'card,bank,mobile_money',
            'Description' => 'Comma-separated list of payment channels: card,bank,ussd,qr,mobile_money,bank_transfer',
        ),
        // Webhook URL Info
        'webhookInfo' => array(
            'FriendlyName' => 'Webhook URL',
            'Type' => 'text',
            'Size' => '80',
            'Default' => '',
            'Description' => 'Configure this URL in your Lahza dashboard: ' . rtrim($_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']), '/') . '/modules/gateways/callback/lahza.php',
        ),
    );
}

/**
 * Payment link.
 *
 * Required by third party payment gateway modules only.
 *
 * Defines the HTML output displayed on an invoice. Typically consists of an
 * HTML form that will take the user to the payment gateway endpoint.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @see https://developers.whmcs.com/payment-gateways/third-party-gateway/
 *
 * @return string
 */
function lahza_link($params)
{
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    $defaultCurrency = $params['defaultCurrency'];
    $paymentChannels = $params['paymentChannels'];

    // Invoice Parameters
    $invoiceId = $params['invoiceid'];
    $description = $params["description"];
    $amount = $params['amount'];
    $currencyCode = $params['currency'];

    // Client Parameters
    $firstname = $params['clientdetails']['firstname'];
    $lastname = $params['clientdetails']['lastname'];
    $email = $params['clientdetails']['email'];
    $address1 = $params['clientdetails']['address1'];
    $address2 = $params['clientdetails']['address2'];
    $city = $params['clientdetails']['city'];
    $state = $params['clientdetails']['state'];
    $postcode = $params['clientdetails']['postcode'];
    $country = $params['clientdetails']['country'];
    $phone = $params['clientdetails']['phonenumber'];

    // System Parameters
    $companyName = $params['companyname'];
    $systemUrl = $params['systemurl'];
    $returnUrl = $params['returnurl'];
    $langPayNow = $params['langpaynow'];
    $moduleDisplayName = $params['name'];
    $moduleName = $params['paymentmethod'];
    $whmcsVersion = $params['whmcsVersion'];

    // Debug: Log initial parameters and all available client data
    $debugInfo = array(
        'invoice_id' => $invoiceId,
        'amount' => $amount,
        'currency' => $currencyCode,
        'email' => $email,
        'phone' => $phone,
        'test_mode' => $testMode,
        'public_key_set' => !empty($publicKey),
        'secret_key_set' => !empty($secretKey),
        'all_client_details' => $params['clientdetails'] // Log all client details to find correct ID
    );
    logTransaction($moduleDisplayName, $debugInfo, 'Debug: Payment Link Called - All Client Data');

    // Validate required configuration
    if (empty($publicKey) || empty($secretKey)) {
        $error = 'Missing API credentials. Please configure your Lahza API keys.';
        logTransaction($moduleDisplayName, array('error' => $error), 'Configuration Error');
        return '<div class="alert alert-danger">' . $error . '</div>';
    }

    // Validate required customer data
    if (empty($email)) {
        $error = 'Customer email is required for payment processing.';
        logTransaction($moduleDisplayName, array('error' => $error), 'Validation Error');
        return '<div class="alert alert-danger">' . $error . '</div>';
    }

    if (empty($phone)) {
        $error = 'Customer phone number is required for payment processing.';
        logTransaction($moduleDisplayName, array('error' => $error), 'Validation Error');
        return '<div class="alert alert-danger">' . $error . '</div>';
    }

    if ($amount <= 0) {
        $error = 'Invalid payment amount.';
        logTransaction($moduleDisplayName, array('error' => $error, 'amount' => $amount), 'Validation Error');
        return '<div class="alert alert-danger">' . $error . '</div>';
    }

    // Check if invoice is already paid to prevent duplicate payments
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("SELECT status FROM tblinvoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoiceStatus = $stmt->fetch();
        
        if ($invoiceStatus && $invoiceStatus['status'] === 'Paid') {
            return '<div class="alert alert-success">
                        <strong>تم الدفع بنجاح!</strong><br>
                        تم دفع هذه الفاتورة بالفعل. شكراً لك!
                        <br><strong>Payment Completed!</strong><br>
                        This invoice has already been paid. Thank you!
                    </div>';
        }
        
        // Check for recent pending transactions to avoid duplicates
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM tblgatewaylog 
            WHERE invoiceid = ? 
            AND gateway = ? 
            AND date > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            AND action LIKE '%Transaction Initialized%'
        ");
        $stmt->execute([$invoiceId, $moduleDisplayName]);
        $recentTransactions = $stmt->fetch();
        
        if ($recentTransactions && $recentTransactions['count'] > 0) {
            logTransaction($moduleDisplayName, array(
                'invoice_id' => $invoiceId,
                'recent_count' => $recentTransactions['count']
            ), 'Recent transaction found - using cached attempt');
        }
        
    } catch (Exception $e) {
        logTransaction($moduleDisplayName, array('error' => $e->getMessage()), 'Error checking invoice status');
    }

    try {
        // Initialize Lahza API Handler
        $lahzaApi = new LahzaApiHandler($publicKey, $secretKey, $testMode);

        // Extract correct WHMCS parameters according to task requirements
        $invoiceId = $params['invoiceid'];
        // Fix: Use correct key for client ID - WHMCS uses 'id' not 'userid'
        $clientId = isset($params['clientdetails']['id']) ? $params['clientdetails']['id'] :
                   (isset($params['clientdetails']['userid']) ? $params['clientdetails']['userid'] :
                   (isset($params['clientdetails']['client_id']) ? $params['clientdetails']['client_id'] : null));
        $amount = (int)($params['amount'] * 100); // Convert to agorot (cents)
        $clientName = $params['clientdetails']['firstname'] . ' ' . $params['clientdetails']['lastname'];
        $clientEmail = $params['clientdetails']['email'];
        
        // Clean phone number with better validation
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);
        if (empty($cleanPhone) || strlen($cleanPhone) < 8) {
            // Try to get phone from other sources or use a more realistic default
            $cleanPhone = !empty($phone) ? '+972000000000' : '+972000000000'; // Default Israeli number
            logTransaction($moduleDisplayName, array(
                'original_phone' => $phone,
                'cleaned_phone' => $cleanPhone,
                'warning' => 'Phone number was empty or invalid, using default'
            ), 'Phone Number Warning');
        }
        
        // Create unique reference to avoid duplicates - ensure it's URL safe
        $uniqueReference = 'INV-' . $invoiceId . '-' . time() . '-' . substr(md5($invoiceId . $clientEmail . microtime()), 0, 8);

        // Validate client ID exists
        if (empty($clientId)) {
            logTransaction($moduleDisplayName, array(
                'warning' => 'Client ID not found in clientdetails',
                'available_keys' => array_keys($params['clientdetails']),
                'invoice_id' => $invoiceId
            ), 'Client ID Warning');
            $clientId = 0; // Default fallback
        }
        
        // Prepare transaction data according to Lahza API specifications
        $transactionData = array(
            'email' => $clientEmail,
            'mobile' => $cleanPhone,
            'amount' => $amount,
            'currency' => $currencyCode ?: $defaultCurrency,
            'reference' => $uniqueReference,
            'callback_url' => $systemUrl . '/modules/gateways/callback/lahza.php',
            'redirect_url' => $returnUrl,
            'first_name' => $firstname,
            'last_name' => $lastname,
            'description' => $description, // Add description at top level
            'customer' => array(
                'id' => $clientId,
                'name' => $clientName,
                'email' => $clientEmail,
                'phone' => $cleanPhone,
                'first_name' => $firstname,
                'last_name' => $lastname
            ),
            'metadata' => array(
                'invoiceid' => $invoiceId,
                'clientid' => $clientId,
                'description' => $description,
                'company' => $companyName,
                'original_reference' => 'INV-' . $invoiceId,
                'whmcs_version' => $whmcsVersion,
                'gateway_version' => '1.0.0'
            )
        );

        // Log transaction data being sent for debugging
        logTransaction($moduleDisplayName, $transactionData, 'Debug: Transaction Data Prepared');

        // Add payment channels if specified
        if (!empty($paymentChannels)) {
            $channels = array_map('trim', explode(',', $paymentChannels));
            $transactionData['channels'] = $channels;
        }

        // Initialize transaction
        $response = $lahzaApi->initializeTransaction($transactionData);

        // Log the full response for debugging
        logTransaction($moduleDisplayName, $response, 'Debug: API Response Received');

        // Check if we have a valid response
        if (!$response) {
            $error = 'No response received from payment gateway';
            logTransaction($moduleDisplayName, array('error' => $error), 'API Error');
            return '<div class="alert alert-danger">' . $error . '</div>';
        }

        // Log the complete response structure for debugging
        logTransaction($moduleDisplayName, array(
            'response_keys' => array_keys($response),
            'response_structure' => getResponseStructure($response),
            'has_data' => isset($response['data']),
            'data_keys' => isset($response['data']) ? array_keys($response['data']) : 'N/A'
        ), 'Complete Response Structure Analysis');

        // Check for authorization URL in various possible response structures
        $authorizationUrl = null;

        // Try different possible response structures from Lahza API
        if (isset($response['data']['authorization_url'])) {
            $authorizationUrl = $response['data']['authorization_url'];
        } elseif (isset($response['authorization_url'])) {
            $authorizationUrl = $response['authorization_url'];
        } elseif (isset($response['data']['checkout_url'])) {
            $authorizationUrl = $response['data']['checkout_url'];
        } elseif (isset($response['checkout_url'])) {
            $authorizationUrl = $response['checkout_url'];
        } elseif (isset($response['data']['payment_url'])) {
            $authorizationUrl = $response['data']['payment_url'];
        } elseif (isset($response['payment_url'])) {
            $authorizationUrl = $response['payment_url'];
        } elseif (isset($response['data']['link'])) {
            $authorizationUrl = $response['data']['link'];
        } elseif (isset($response['link'])) {
            $authorizationUrl = $response['link'];
        } elseif (isset($response['data']['url'])) {
            $authorizationUrl = $response['data']['url'];
        } elseif (isset($response['url'])) {
            $authorizationUrl = $response['url'];
        }

        // If we found an authorization URL, proceed with payment
        if (!empty($authorizationUrl)) {
            // Log successful initialization
            logTransaction($moduleDisplayName, array(
                'url' => $authorizationUrl,
                'response_structure' => array_keys($response)
            ), 'Authorization URL Found - Transaction Initialized Successfully');

            // Return redirect form with enhanced styling for WIDDX template
            $htmlOutput = '<div class="payment-gateway-form lahza-payment-form widdx-integration">';
            
            // Add payment method info
            $htmlOutput .= '<div class="lahza-gateway-info">';
            $htmlOutput .= '<span class="gateway-name">Secure Payment via Lahza</span>';
            $htmlOutput .= '</div>';
            
            // Simple and reliable payment form
            $htmlOutput .= '<form method="get" action="' . htmlspecialchars($authorizationUrl) . '" class="payment-form lahza-form">';
            $htmlOutput .= '<button type="submit" class="btn btn-primary btn-lg payment-button lahza-pay-button">';
            $htmlOutput .= htmlspecialchars($langPayNow);
            $htmlOutput .= '</button>';
            $htmlOutput .= '</form>';
            
            // Add supported payment methods info
            if (!empty($paymentChannels)) {
                $channels = array_map('trim', explode(',', $paymentChannels));
                $channelNames = array(
                    'card' => 'Credit/Debit Cards',
                    'bank' => 'Bank Transfer',
                    'mobile_money' => 'Mobile Money',
                    'qr' => 'QR Code',
                    'ussd' => 'USSD',
                    'bank_transfer' => 'Direct Bank Transfer'
                );
                
                $htmlOutput .= '<div class="supported-methods">';
                $htmlOutput .= '<small>Supports: ';
                $supportedList = array();
                foreach ($channels as $channel) {
                    if (isset($channelNames[$channel])) {
                        $supportedList[] = $channelNames[$channel];
                    }
                }
                $htmlOutput .= implode(', ', $supportedList);
                $htmlOutput .= '</small>';
                $htmlOutput .= '</div>';
            }
            
            $htmlOutput .= '</div>';

            // Enhanced styling for WIDDX template integration
            $htmlOutput .= '<style>';
            $htmlOutput .= '.lahza-payment-form { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef; text-align: center; }';
            $htmlOutput .= '.lahza-gateway-info { margin-bottom: 15px; font-size: 14px; color: #6c757d; }';
            $htmlOutput .= '.lahza-gateway-info::before { content: "🔒"; margin-right: 5px; }';
            $htmlOutput .= '.lahza-pay-button { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; border: none !important; color: white !important; padding: 15px 30px !important; font-size: 16px !important; font-weight: 600 !important; border-radius: 6px !important; cursor: pointer !important; transition: all 0.3s ease !important; min-width: 200px !important; text-transform: uppercase !important; letter-spacing: 0.5px !important; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important; text-decoration: none !important; display: inline-block !important; }';
            $htmlOutput .= '.lahza-pay-button:hover { transform: translateY(-2px) !important; box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important; opacity: 0.95 !important; color: white !important; text-decoration: none !important; }';
            $htmlOutput .= '.lahza-pay-button:active { transform: translateY(0) !important; box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3) !important; }';
            $htmlOutput .= '.lahza-pay-button:focus { outline: 2px solid #667eea !important; outline-offset: 2px !important; color: white !important; }';
            $htmlOutput .= '.supported-methods { margin-top: 10px; font-size: 12px; color: #6c757d; }';
            $htmlOutput .= '@media (max-width: 768px) { .lahza-payment-form { margin: 15px 0; padding: 15px; } .lahza-pay-button { width: 100% !important; max-width: 300px !important; padding: 12px 20px !important; font-size: 14px !important; } }';
            
            // WIDDX theme integration
            $htmlOutput .= '.widdx-theme .lahza-payment-form { background: var(--widdx-card-bg, #ffffff); border: 1px solid var(--widdx-border-color, #e9ecef); box-shadow: var(--widdx-card-shadow, 0 2px 4px rgba(0,0,0,0.1)); }';
            $htmlOutput .= '.widdx-theme .lahza-pay-button { background: var(--widdx-primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%)) !important; }';
            
            // Dark mode support
            $htmlOutput .= '@media (prefers-color-scheme: dark) { .lahza-payment-form { background: #2d3748; border-color: #4a5568; color: #e2e8f0; } .lahza-pay-button { background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important; } }';
            $htmlOutput .= '</style>';
            
            // Add simple JavaScript for form submission feedback
            $htmlOutput .= '<script>';
            $htmlOutput .= 'document.addEventListener("DOMContentLoaded", function() {';
            $htmlOutput .= '    var form = document.querySelector(".lahza-form");';
            $htmlOutput .= '    if (form) {';
            $htmlOutput .= '        form.addEventListener("submit", function() {';
            $htmlOutput .= '            var btn = this.querySelector(".lahza-pay-button");';
            $htmlOutput .= '            if (btn) {';
            $htmlOutput .= '                btn.innerHTML = "⏳ Redirecting...";';
            $htmlOutput .= '                btn.style.opacity = "0.8";';
            $htmlOutput .= '            }';
            $htmlOutput .= '        });';
            $htmlOutput .= '    }';
            $htmlOutput .= '});';
            $htmlOutput .= '</script>';

            return $htmlOutput;
        }

        // If no authorization URL found, check for error messages
        $errorMessage = 'Unknown error occurred';
        $debugInfo = 'No authorization URL found in response';
        
        // Check various error message formats
        if (isset($response['message'])) {
            $errorMessage = $response['message'];
        } elseif (isset($response['error'])) {
            $errorMessage = $response['error'];
        } elseif (isset($response['errors'])) {
            $errorMessage = is_array($response['errors']) ? implode(', ', $response['errors']) : $response['errors'];
        } elseif (isset($response['data']['message'])) {
            $errorMessage = $response['data']['message'];
        } elseif (isset($response['data']['error'])) {
            $errorMessage = $response['data']['error'];
        }

        // Check if this is actually a successful response but with different structure
        $isSuccess = false;
        if (isset($response['status'])) {
            $isSuccess = in_array(strtolower($response['status']), ['success', 'successful', 'ok', 'true']);
        } elseif (isset($response['success'])) {
            $isSuccess = $response['success'] === true || $response['success'] === 'true';
        } elseif (isset($response['data']['status'])) {
            $isSuccess = in_array(strtolower($response['data']['status']), ['success', 'successful', 'ok', 'true']);
        }

        if ($isSuccess) {
            $debugInfo = 'Success status found but no authorization URL';
        }
        
        logTransaction($moduleDisplayName, $response, 'Transaction Initialization Failed: ' . $errorMessage . ' | ' . $debugInfo);
        
        // Return user-friendly error with debug info in test mode
        $errorDisplay = 'Payment initialization failed. Please try again or contact support.';
        if ($testMode) {
            $errorDisplay .= '<br><small>Debug: ' . htmlspecialchars($errorMessage) . '</small>';
            $errorDisplay .= '<br><small>Response Keys: ' . htmlspecialchars(implode(', ', array_keys($response))) . '</small>';
        }
        
        return '<div class="alert alert-danger">' . $errorDisplay . '</div>';

    } catch (Exception $e) {
        // Log detailed exception information
        $exceptionData = array(
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        );
        logTransaction($moduleDisplayName, $exceptionData, 'Exception in Payment Link');
        
        // Return user-friendly error with debug info in test mode
        $errorDisplay = 'Payment service temporarily unavailable. Please try again later.';
        if ($testMode) {
            $errorDisplay .= '<br><small>Debug: ' . htmlspecialchars($e->getMessage()) . '</small>';
        }
        
        return '<div class="alert alert-danger">' . $errorDisplay . '</div>';
    }
}

/**
 * Refund transaction.
 *
 * Called when a refund is requested for a previously successful transaction.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @see https://developers.whmcs.com/payment-gateways/refunds/
 *
 * @return array Transaction response status
 */
function lahza_refund($params)
{
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];

    // Transaction Parameters
    $transactionIdToRefund = $params['transid'];
    $refundAmount = $params['amount'];
    $currencyCode = $params['currency'];

    // System Parameters
    $moduleDisplayName = $params['name'];

    try {
        // Initialize Lahza API Handler
        $lahzaApi = new LahzaApiHandler($publicKey, $secretKey, $testMode);

        // Convert amount to lowest currency unit (multiply by 100)
        $refundAmountInCents = (int)($refundAmount * 100);

        // Prepare refund data
        $refundData = array(
            'transaction' => $transactionIdToRefund,
            'amount' => $refundAmountInCents,
            'reason' => 'Refund requested via WHMCS'
        );

        // Process refund
        $response = $lahzaApi->processRefund($refundData);

        if ($response && isset($response['status']) && $response['status'] === 'success') {
            // Log successful refund
            logTransaction($moduleDisplayName, $response, 'Refund Successful');

            return array(
                'status' => 'success',
                'rawdata' => $response,
                'transid' => isset($response['data']['id']) ? $response['data']['id'] : $transactionIdToRefund,
                'fees' => isset($response['data']['fee']) ? ($response['data']['fee'] / 100) : 0,
            );
        } else {
            // Log refund failure
            $errorMessage = isset($response['message']) ? $response['message'] : 'Refund failed';
            logTransaction($moduleDisplayName, $response, 'Refund Failed: ' . $errorMessage);

            return array(
                'status' => 'declined',
                'rawdata' => $response,
            );
        }

    } catch (Exception $e) {
        // Log exception
        logTransaction($moduleDisplayName, array('error' => $e->getMessage()), 'Refund Exception: ' . $e->getMessage());

        return array(
            'status' => 'error',
            'rawdata' => array('error' => $e->getMessage()),
        );
    }
}

/**
 * Cancel subscription.
 *
 * If the payment gateway creates subscriptions and stores the subscription
 * ID in tblhosting.subscriptionid, this function is called upon cancellation
 * or request by an admin user.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @see https://developers.whmcs.com/payment-gateways/subscription-management/
 *
 * @return array Transaction response status
 */
function lahza_cancelSubscription($params)
{
    // Gateway Configuration Parameters
    $publicKey = $params['publicKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];

    // Subscription Parameters
    $subscriptionIdToCancel = $params['subscriptionID'];

    // System Parameters
    $moduleDisplayName = $params['name'];

    try {
        // Initialize Lahza API Handler
        $lahzaApi = new LahzaApiHandler($publicKey, $secretKey, $testMode);

        // Cancel subscription (if Lahza supports recurring payments)
        $response = $lahzaApi->cancelSubscription($subscriptionIdToCancel);

        if ($response && isset($response['status']) && $response['status'] === 'success') {
            // Log successful cancellation
            logTransaction($moduleDisplayName, $response, 'Subscription Cancelled');

            return array(
                'status' => 'success',
                'rawdata' => $response,
            );
        } else {
            // Log cancellation failure
            $errorMessage = isset($response['message']) ? $response['message'] : 'Subscription cancellation failed';
            logTransaction($moduleDisplayName, $response, 'Subscription Cancellation Failed: ' . $errorMessage);

            return array(
                'status' => 'error',
                'rawdata' => $response,
            );
        }

    } catch (Exception $e) {
        // Log exception
        logTransaction($moduleDisplayName, array('error' => $e->getMessage()), 'Subscription Cancellation Exception: ' . $e->getMessage());

        return array(
            'status' => 'error',
            'rawdata' => array('error' => $e->getMessage()),
        );
    }
}

/**
 * Helper function to analyze response structure
 *
 * @param array $response API response
 * @param int $maxDepth Maximum depth to analyze
 * @param int $currentDepth Current depth level
 * @return array Simplified structure analysis
 */
function getResponseStructure($response, $maxDepth = 2, $currentDepth = 0) {
    if ($currentDepth >= $maxDepth || !is_array($response)) {
        return gettype($response);
    }

    $structure = array();
    foreach ($response as $key => $value) {
        if (is_array($value)) {
            $structure[$key] = getResponseStructure($value, $maxDepth, $currentDepth + 1);
        } else {
            $structure[$key] = gettype($value);
        }
    }

    return $structure;
}