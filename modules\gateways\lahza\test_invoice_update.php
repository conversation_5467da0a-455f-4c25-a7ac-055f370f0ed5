<?php
/**
 * Lahza Invoice Update Test Tool
 * 
 * Test invoice status updates and payment processing
 * Access: https://yourdomain.com/modules/gateways/lahza/test_invoice_update.php
 */

// Security check
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['adminid']) && !empty($_SESSION['adminid'])) {
    $isAdmin = true;
}

if (!$isAdmin && !isset($_GET['debug_key']) || (isset($_GET['debug_key']) && $_GET['debug_key'] !== 'lahza_debug_2025')) {
    die('Access denied. Admin login required or provide debug key.');
}

$action = $_POST['action'] ?? '';
$invoiceId = $_POST['invoice_id'] ?? '';
$message = '';
$messageType = '';

// Handle test actions
if ($action && $invoiceId) {
    try {
        switch ($action) {
            case 'test_payment':
                $transactionId = 'TEST_' . time();
                $amount = $_POST['amount'] ?? 50.00;
                $fee = $_POST['fee'] ?? 1.50;
                
                // Test adding payment to invoice
                $result = addInvoicePayment(
                    $invoiceId,
                    $transactionId,
                    $amount,
                    $fee,
                    'lahza'
                );
                
                if ($result === 'success') {
                    $message = "Payment added successfully! Transaction ID: {$transactionId}";
                    $messageType = "success";
                } else {
                    $message = "Payment addition result: " . $result;
                    $messageType = "warning";
                }
                break;
                
            case 'check_invoice':
                // Check invoice status
                $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
                $stmt = $pdo->prepare("SELECT * FROM tblinvoices WHERE id = ?");
                $stmt->execute([$invoiceId]);
                $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($invoice) {
                    $message = "Invoice found! Status: " . $invoice['status'] . ", Total: " . $invoice['total'];
                    $messageType = "info";
                } else {
                    $message = "Invoice not found!";
                    $messageType = "error";
                }
                break;
                
            case 'simulate_webhook':
                // Simulate webhook call
                $webhookData = [
                    'event' => 'charge.success',
                    'data' => [
                        'id' => 'TEST_' . time(),
                        'status' => 'success',
                        'amount' => ($_POST['amount'] ?? 50.00) * 100, // Convert to cents
                        'currency' => 'USD',
                        'reference' => 'INV-' . $invoiceId . '-' . time(),
                        'metadata' => [
                            'invoiceid' => $invoiceId,
                            'clientid' => '1'
                        ]
                    ]
                ];
                
                $webhookUrl = 'https://' . $_SERVER['HTTP_HOST'] . '/modules/gateways/callback/lahza.php';
                $payload = json_encode($webhookData);
                
                // Get gateway settings for signature
                $gatewayParams = getGatewayVariables('lahza');
                $signature = hash_hmac('sha256', $payload, $gatewayParams['secretKey']);
                
                // Send webhook
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $webhookUrl);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'X-Lahza-Signature: ' . $signature
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                $message = "Webhook sent! HTTP {$httpCode}. Response: " . $response;
                $messageType = ($httpCode == 200) ? "success" : "error";
                break;
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = "error";
    }
}

// Get recent invoices for testing
$recentInvoices = [];
try {
    $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
    $stmt = $pdo->prepare("SELECT id, userid, status, total, duedate FROM tblinvoices ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $recentInvoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Handle error
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Invoice Update Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #dc3545; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.danger { background: #dc3545; }
        .btn.success { background: #28a745; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table th, table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        table th { background: #f8f9fa; }
        .status-paid { color: #28a745; font-weight: bold; }
        .status-unpaid { color: #dc3545; font-weight: bold; }
        .status-cancelled { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Lahza Invoice Update Test</h1>
            <p>Test and diagnose invoice status update issues</p>
        </div>

        <?php if ($message): ?>
            <div class="test-section <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>📋 Recent Invoices</h2>
            <table>
                <thead>
                    <tr>
                        <th>Invoice ID</th>
                        <th>User ID</th>
                        <th>Status</th>
                        <th>Total</th>
                        <th>Due Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentInvoices as $invoice): ?>
                        <tr>
                            <td><?= $invoice['id'] ?></td>
                            <td><?= $invoice['userid'] ?></td>
                            <td class="status-<?= strtolower($invoice['status']) ?>"><?= $invoice['status'] ?></td>
                            <td>$<?= $invoice['total'] ?></td>
                            <td><?= $invoice['duedate'] ?></td>
                            <td>
                                <button onclick="selectInvoice(<?= $invoice['id'] ?>, <?= $invoice['total'] ?>)" class="btn">Select</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🧪 Test Invoice Payment</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="invoice_id">Invoice ID:</label>
                    <input type="number" id="invoice_id" name="invoice_id" value="<?= htmlspecialchars($invoiceId) ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="amount">Amount:</label>
                    <input type="number" id="amount" name="amount" step="0.01" value="50.00" required>
                </div>
                
                <div class="form-group">
                    <label for="fee">Fee:</label>
                    <input type="number" id="fee" name="fee" step="0.01" value="1.50">
                </div>
                
                <button type="submit" name="action" value="check_invoice" class="btn">Check Invoice</button>
                <button type="submit" name="action" value="test_payment" class="btn success">Add Test Payment</button>
                <button type="submit" name="action" value="simulate_webhook" class="btn danger">Simulate Webhook</button>
            </form>
        </div>

        <div class="test-section">
            <h2>🔍 Diagnostic Steps</h2>
            <div class="info">
                <h3>To diagnose invoice update issues:</h3>
                <ol>
                    <li><strong>Check Invoice:</strong> Verify invoice exists and current status</li>
                    <li><strong>Test Payment:</strong> Manually add payment to see if function works</li>
                    <li><strong>Simulate Webhook:</strong> Test complete webhook flow</li>
                    <li><strong>Check Logs:</strong> Review gateway logs for errors</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Related Tools</h2>
            <a href="view_logs.php?debug_key=lahza_debug_2025" target="_blank" class="btn">View Logs</a>
            <a href="test_webhook.php?debug_key=lahza_debug_2025" target="_blank" class="btn">Webhook Test</a>
            <a href="../../../admin/logs.php?type=gateway" target="_blank" class="btn">WHMCS Gateway Logs</a>
            <a href="../../../admin/invoices.php" target="_blank" class="btn">WHMCS Invoices</a>
        </div>

        <div class="test-section warning">
            <h2>⚠️ Common Issues</h2>
            <ul>
                <li><strong>Webhook not received:</strong> Check webhook URL configuration in Lahza dashboard</li>
                <li><strong>Signature verification fails:</strong> Verify secret key is correct</li>
                <li><strong>Invoice ID not found:</strong> Check metadata extraction in webhook</li>
                <li><strong>Duplicate transaction:</strong> Transaction ID already processed</li>
                <li><strong>Gateway not activated:</strong> Ensure Lahza gateway is active in WHMCS</li>
            </ul>
        </div>
    </div>

    <script>
        function selectInvoice(invoiceId, amount) {
            document.getElementById('invoice_id').value = invoiceId;
            document.getElementById('amount').value = amount;
        }
    </script>
</body>
</html>
