# 🔧 Lahza Gateway - Invoice Update Fix Guide

## ❌ Problem: Invoice Not Updating After Successful Payment

**Symptoms:**
- Payment completed successfully on Lahza
- Customer redirected back to WHMCS
- Invoice still shows as "UNPAID"
- No payment recorded in WHMCS

---

## 🔍 Root Cause Analysis

This issue is typically caused by **webhook problems**. The payment flow works like this:

```
Customer Pays → Lahza Processes → Webhook Sent → WHMCS Updates Invoice
                                      ↑
                                 Problem Here!
```

---

## 🚀 Quick Diagnosis Tools

### 1. **Webhook Debug Tool**
```
https://yourdomain.com/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025
```
**Checks:**
- Webhook URL accessibility
- Gateway configuration
- Recent webhook activity

### 2. **Invoice Update Test**
```
https://yourdomain.com/modules/gateways/lahza/test_invoice_update.php?debug_key=lahza_debug_2025
```
**Tests:**
- Manual payment addition
- Invoice status checking
- Webhook simulation

---

## 🔧 Step-by-Step Fix

### Step 1: Verify Webhook URL Configuration

**In Lahza Dashboard:**
1. Login to your Lahza merchant dashboard
2. Go to **Settings → Webhooks**
3. Ensure webhook URL is set to:
   ```
   https://yourdomain.com/modules/gateways/callback/lahza.php
   ```
4. **Important:** URL must use HTTPS (not HTTP)

### Step 2: Test Webhook Accessibility

**Run this test:**
```
https://yourdomain.com/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025
```

**Expected Results:**
- ✅ Webhook URL accessible (HTTP 200)
- ✅ Gateway configured properly
- ✅ SSL certificate valid

### Step 3: Check Gateway Configuration

**In WHMCS Admin:**
1. Go to **Setup → Payments → Payment Gateways**
2. Find "Lahza Payment Gateway"
3. Verify:
   - ✅ Gateway is **Activated**
   - ✅ **Public Key** is set correctly
   - ✅ **Secret Key** is set correctly
   - ✅ **Test Mode** matches your keys

### Step 4: Test Manual Payment Addition

**Use Invoice Update Test:**
1. Go to test tool
2. Select an unpaid invoice
3. Click "Add Test Payment"
4. Check if invoice updates

**If this works:** Problem is with webhook reception
**If this fails:** Problem is with WHMCS configuration

### Step 5: Simulate Webhook

**In Invoice Update Test:**
1. Select the same invoice
2. Click "Simulate Webhook"
3. Check response and logs

---

## 🐛 Common Issues & Solutions

### Issue 1: Webhook URL Not Accessible

**Symptoms:**
- Webhook debug shows HTTP error
- No webhook logs in WHMCS

**Solutions:**
1. **Check SSL Certificate:**
   ```bash
   curl -I https://yourdomain.com/modules/gateways/callback/lahza.php
   ```

2. **Verify File Permissions:**
   - Callback file must be readable by web server
   - Check file exists and has correct permissions

3. **Check Firewall:**
   - Ensure incoming HTTPS requests are allowed
   - Whitelist Lahza IP addresses if needed

### Issue 2: Signature Verification Fails

**Symptoms:**
- Webhook received but not processed
- "Invalid signature" in logs

**Solutions:**
1. **Verify Secret Key:**
   - Must match exactly between WHMCS and Lahza
   - No extra spaces or characters

2. **Check Headers:**
   - Webhook must include `X-Lahza-Signature` header
   - Signature calculation must be correct

### Issue 3: Invoice ID Not Found

**Symptoms:**
- Webhook processed but invoice not updated
- "Invoice not found" in logs

**Solutions:**
1. **Check Metadata:**
   - Ensure `invoiceid` is included in payment metadata
   - Verify invoice ID format is correct

2. **Test Reference Extraction:**
   - Check if invoice ID can be extracted from reference field

### Issue 4: Duplicate Transaction

**Symptoms:**
- Webhook received but payment not added
- "Duplicate transaction" in logs

**Solutions:**
1. **Check Transaction IDs:**
   - Each payment must have unique transaction ID
   - Clear duplicate entries if needed

---

## 🔍 Advanced Debugging

### Check WHMCS Gateway Logs

**Location:** Admin → Utilities → Logs → Gateway Log

**Look for:**
- Webhook reception entries
- Error messages
- Payment addition results

### Check Server Error Logs

**Common locations:**
- `/var/log/apache2/error.log`
- `/var/log/nginx/error.log`
- cPanel Error Logs

### Test with cURL

**Manual webhook test:**
```bash
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -H "X-Lahza-Signature: YOUR_SIGNATURE" \
  -d '{"event":"charge.success","data":{"id":"TEST123","status":"success","metadata":{"invoiceid":"10"}}}'
```

---

## ✅ Verification Checklist

After applying fixes, verify:

- [ ] Webhook URL accessible via HTTPS
- [ ] Gateway properly configured in WHMCS
- [ ] Secret key matches between systems
- [ ] Test payment updates invoice correctly
- [ ] Webhook simulation works
- [ ] Real payment test completes successfully

---

## 🚨 Emergency Workaround

**If webhook cannot be fixed immediately:**

### Manual Payment Addition

1. **Get Transaction Details:**
   - Login to Lahza dashboard
   - Find successful transaction
   - Note transaction ID and amount

2. **Add Payment Manually:**
   - Go to WHMCS Admin → Invoices
   - Find the invoice
   - Click "Add Payment"
   - Enter transaction details

3. **Mark as Paid:**
   - Select payment method: "Lahza Payment Gateway"
   - Enter transaction ID from Lahza
   - Save payment

---

## 📞 Getting Help

### Self-Diagnosis Tools
1. **Webhook Debug:** `debug_webhook.php`
2. **Invoice Test:** `test_invoice_update.php`
3. **Log Viewer:** `view_logs.php`

### Contact Support
**Provide this information:**
- Webhook debug results
- WHMCS gateway logs
- Lahza transaction ID
- Invoice ID that's not updating
- Error messages from logs

### Lahza Support
- Check webhook configuration in dashboard
- Verify webhook events are being sent
- Confirm signature calculation method

---

## 🎯 Prevention

**To prevent future issues:**

1. **Regular Testing:**
   - Test payments monthly
   - Monitor webhook logs
   - Verify SSL certificate validity

2. **Monitoring:**
   - Set up alerts for failed webhooks
   - Monitor invoice update rates
   - Check logs regularly

3. **Backup Plan:**
   - Document manual payment process
   - Train staff on troubleshooting
   - Keep contact information updated

---

**🎉 Most Common Solution:** Configure webhook URL correctly in Lahza dashboard with proper HTTPS and verify secret key matches.

*This resolves 90% of invoice update issues.*
