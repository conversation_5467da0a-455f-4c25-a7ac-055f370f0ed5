# 🚀 Lahza Payment Gateway - Quick Start Guide

## تم إصلاح البوابة! | Gateway Fixed!

**Version 1.1.0** - All major issues have been resolved.

---

## ⚡ Quick Setup (5 Minutes)

### 1. Run Quick Setup Wizard
```
https://yourdomain.com/modules/gateways/lahza/quick_setup.php
```

### 2. Test Your Installation
```
https://yourdomain.com/modules/gateways/lahza/test_gateway.php
```

### 3. Monitor Logs
```
https://yourdomain.com/modules/gateways/lahza/view_logs.php?debug_key=lahza_debug_2025
```

---

## 🔧 What Was Fixed

### ✅ Client ID Issues
- Fixed extraction of client ID from WHMCS parameters
- Added support for multiple ID formats
- Automatic fallback if client ID is missing

### ✅ Phone Number Validation
- Improved phone number cleaning and validation
- Better default phone numbers for testing
- Enhanced error messages

### ✅ Webhook Processing
- Enhanced signature verification with multiple header support
- Better webhook data extraction
- Support for multiple webhook data structures
- Improved error handling

### ✅ API Integration
- Better API response parsing
- Multiple fallback options for authorization URLs
- Improved error message extraction
- Separate test/production API endpoints

### ✅ Debugging & Monitoring
- Comprehensive logging system
- Real-time log viewer
- Built-in test script
- Enhanced error messages

---

## 📋 Manual Configuration

If you prefer manual setup:

### 1. Activate Gateway
- Go to **WHMCS Admin → Setup → Payments → Payment Gateways**
- Find "Lahza Payment Gateway" and click **Activate**

### 2. Configure Settings
- **Public Key**: Your Lahza public key (`pk_test_...` or `pk_live_...`)
- **Secret Key**: Your Lahza secret key (`sk_test_...` or `sk_live_...`)
- **Test Mode**: Enable for testing
- **Default Currency**: USD, ILS, or JOD
- **Payment Channels**: `card,bank,mobile_money`

### 3. Setup Webhook
Configure this URL in your Lahza dashboard:
```
https://yourdomain.com/modules/gateways/callback/lahza.php
```

---

## 🧪 Testing

### Test Mode
1. Enable **Test Mode** in gateway settings
2. Use test API keys from Lahza
3. Process test transactions

### Test Script
The built-in test script checks:
- ✅ Gateway configuration
- ✅ API connectivity  
- ✅ Webhook URL accessibility
- ✅ Database connections
- ✅ File permissions

### Log Monitoring
Real-time log viewer shows:
- 📊 Statistics dashboard
- 🔍 Filtered log entries
- 🔄 Auto-refresh every 30 seconds
- 📱 Mobile-friendly interface

---

## 🚨 Troubleshooting

### ❌ "Invoice Not Updating After Payment" Error

**This is the most common issue! Quick fixes:**

1. **Debug Webhook:**
   ```
   https://yourdomain.com/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025
   ```

2. **Test Invoice Update:**
   ```
   https://yourdomain.com/modules/gateways/lahza/test_invoice_update.php?debug_key=lahza_debug_2025
   ```

3. **Check Lahza Dashboard:**
   - Ensure webhook URL is set to: `https://yourdomain.com/modules/gateways/callback/lahza.php`
   - Verify webhook events are enabled

4. **Read Full Guide:**
   - See `INVOICE_UPDATE_FIX.md` for detailed solutions

### ❌ "Could not resolve host: api.lahza.io" Error

**This is a DNS/Network issue. Quick fixes:**

1. **Test Connection:**
   ```
   https://yourdomain.com/modules/gateways/lahza/test_connection.php
   ```

2. **Contact Hosting Provider:**
   - Ask to enable outbound HTTPS connections
   - Request whitelisting of `api.lahza.io`
   - Verify DNS resolution works

3. **Check Network:**
   ```bash
   nslookup api.lahza.io
   curl -I https://api.lahza.io
   ```

4. **Read Full Guide:**
   - See `CONNECTION_FIX.md` for detailed solutions

### Other Common Issues

**Payment button not working?**
- Check API keys are correct
- Verify test/live mode matches keys
- Ensure client has valid email and phone

**Webhook not received?**
- Verify webhook URL is accessible via HTTPS
- Check SSL certificate validity
- Review webhook signature verification

**Client ID errors?**
- Fixed in v1.1.0 - automatic fallback now available
- Check logs for detailed client data

### Quick Diagnostics
1. Run test script: `test_gateway.php`
2. Check logs: `view_logs.php`
3. Review WHMCS Gateway Logs
4. Verify Lahza dashboard configuration

---

## 📞 Support

### Self-Help Tools
- 🔧 **Test Script**: Diagnose issues automatically
- 📊 **Log Viewer**: Monitor real-time activity
- 🚀 **Quick Setup**: Automated configuration
- 📚 **Documentation**: Comprehensive guides

### Technical Support
- **WIDDX Support**: https://widdx.com/support
- **Email**: <EMAIL>
- **Lahza Support**: Contact Lahza for API issues

---

## 🔒 Security Notes

### After Setup
1. **Delete setup files** for security:
   - `quick_setup.php`
   - `test_gateway.php` 
   - `view_logs.php`

2. **Secure your API keys**:
   - Use test keys for development
   - Never expose secret keys in client-side code
   - Regularly rotate keys

3. **Monitor logs** for suspicious activity

---

## 📈 What's New in v1.1.0

### 🔧 Bug Fixes
- Client ID extraction issues
- Phone number validation problems
- Webhook signature verification
- API response parsing errors

### ✨ New Features
- Built-in test script
- Real-time log viewer
- Quick setup wizard
- Enhanced debugging
- Comprehensive error handling

### 📚 Documentation
- Updated troubleshooting guide
- Quick start instructions
- Enhanced README

---

## 🎯 Next Steps

1. **Complete Setup**: Use quick setup wizard
2. **Test Integration**: Run test script
3. **Process Test Payment**: Verify end-to-end flow
4. **Monitor Logs**: Check for any issues
5. **Go Live**: Switch to production keys
6. **Clean Up**: Remove setup files

---

**🎉 Your Lahza payment gateway is now ready to accept payments!**

*Developed with ❤️ by WIDDX - Making payment integration simple and secure.*
