<?php
/**
 * Lahza Payment Gateway Debug Logger
 * 
 * Enhanced logging utility for debugging Lahza payment gateway issues
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

class LahzaDebugLogger
{
    private static $logFile = null;
    private static $enabled = true;
    
    /**
     * Initialize the logger
     */
    public static function init()
    {
        if (self::$logFile === null) {
            $logDir = __DIR__ . '/logs';
            if (!is_dir($logDir)) {
                @mkdir($logDir, 0755, true);
            }
            self::$logFile = $logDir . '/lahza_debug_' . date('Y-m-d') . '.log';
        }
    }
    
    /**
     * Log a debug message
     *
     * @param string $level Log level (INFO, WARNING, ERROR, DEBUG)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    public static function log($level, $message, $context = [])
    {
        if (!self::$enabled) {
            return;
        }
        
        self::init();
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;
        
        // Write to file
        @file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also log to WHMCS if available
        if (function_exists('logTransaction')) {
            logTransaction('Lahza Debug', $context, "{$level}: {$message}");
        }
    }
    
    /**
     * Log info message
     */
    public static function info($message, $context = [])
    {
        self::log('INFO', $message, $context);
    }
    
    /**
     * Log warning message
     */
    public static function warning($message, $context = [])
    {
        self::log('WARNING', $message, $context);
    }
    
    /**
     * Log error message
     */
    public static function error($message, $context = [])
    {
        self::log('ERROR', $message, $context);
    }
    
    /**
     * Log debug message
     */
    public static function debug($message, $context = [])
    {
        self::log('DEBUG', $message, $context);
    }
    
    /**
     * Log API request
     */
    public static function logApiRequest($method, $url, $data = null, $headers = [])
    {
        self::log('API_REQUEST', "API Request: {$method} {$url}", [
            'method' => $method,
            'url' => $url,
            'data' => $data,
            'headers' => $headers,
            'timestamp' => microtime(true)
        ]);
    }
    
    /**
     * Log API response
     */
    public static function logApiResponse($url, $httpCode, $response, $duration = null)
    {
        self::log('API_RESPONSE', "API Response: {$httpCode} for {$url}", [
            'url' => $url,
            'http_code' => $httpCode,
            'response' => $response,
            'duration' => $duration,
            'response_size' => strlen($response ?? ''),
            'timestamp' => microtime(true)
        ]);
    }
    
    /**
     * Log webhook received
     */
    public static function logWebhook($headers, $payload, $signature = null)
    {
        self::log('WEBHOOK', 'Webhook received', [
            'headers' => $headers,
            'payload' => $payload,
            'signature' => $signature,
            'payload_size' => strlen($payload ?? ''),
            'timestamp' => microtime(true)
        ]);
    }
    
    /**
     * Log payment processing
     */
    public static function logPayment($invoiceId, $transactionId, $amount, $status, $details = [])
    {
        self::log('PAYMENT', "Payment processing: Invoice {$invoiceId}, Status: {$status}", [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'status' => $status,
            'details' => $details,
            'timestamp' => microtime(true)
        ]);
    }
    
    /**
     * Get recent log entries
     */
    public static function getRecentLogs($lines = 100)
    {
        self::init();
        
        if (!file_exists(self::$logFile)) {
            return [];
        }
        
        $content = file_get_contents(self::$logFile);
        $logLines = explode(PHP_EOL, $content);
        $logLines = array_filter($logLines); // Remove empty lines
        
        return array_slice($logLines, -$lines);
    }
    
    /**
     * Clear old log files
     */
    public static function cleanup($daysToKeep = 7)
    {
        $logDir = __DIR__ . '/logs';
        if (!is_dir($logDir)) {
            return;
        }
        
        $files = glob($logDir . '/lahza_debug_*.log');
        $cutoffTime = time() - ($daysToKeep * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                @unlink($file);
            }
        }
    }
    
    /**
     * Enable/disable logging
     */
    public static function setEnabled($enabled)
    {
        self::$enabled = $enabled;
    }
    
    /**
     * Get log file path
     */
    public static function getLogFile()
    {
        self::init();
        return self::$logFile;
    }
    
    /**
     * Format data for logging
     */
    private static function formatData($data)
    {
        if (is_array($data) || is_object($data)) {
            return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }
        return (string)$data;
    }
    
    /**
     * Log system information
     */
    public static function logSystemInfo()
    {
        self::log('SYSTEM', 'System Information', [
            'php_version' => PHP_VERSION,
            'whmcs_version' => defined('WHMCS_VERSION') ? WHMCS_VERSION : 'Unknown',
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'curl_version' => function_exists('curl_version') ? curl_version()['version'] : 'Not available',
            'openssl_version' => OPENSSL_VERSION_TEXT,
            'timestamp' => microtime(true)
        ]);
    }
}

// Auto-cleanup old logs when class is loaded
LahzaDebugLogger::cleanup();
