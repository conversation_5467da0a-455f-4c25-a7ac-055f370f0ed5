<?php
/**
 * Quick Network Check for Lahza API
 * 
 * Simple one-page network diagnostic
 */

header('Content-Type: text/plain');

echo "=== Lahza API Network Check ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

// 1. DNS Resolution Test
echo "1. DNS Resolution Test:\n";
$host = 'api.lahza.io';
$ip = gethostbyname($host);

if ($ip !== $host) {
    echo "✅ SUCCESS: $host resolves to $ip\n";
} else {
    echo "❌ FAILED: Cannot resolve $host\n";
    echo "   This indicates DNS issues. Contact your hosting provider.\n";
}
echo "\n";

// 2. Basic Connectivity Test
echo "2. Basic Connectivity Test:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_USERAGENT, 'Lahza-Network-Check/1.0');

$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

if ($error) {
    echo "❌ FAILED: $error\n";
    
    if (strpos($error, 'Could not resolve host') !== false) {
        echo "   → DNS resolution problem\n";
        echo "   → Contact hosting provider to enable DNS resolution\n";
    } elseif (strpos($error, 'Connection timed out') !== false) {
        echo "   → Network timeout\n";
        echo "   → Check firewall settings\n";
    } elseif (strpos($error, 'SSL') !== false) {
        echo "   → SSL/TLS problem\n";
        echo "   → Check SSL certificate configuration\n";
    }
} else {
    echo "✅ SUCCESS: HTTP $httpCode\n";
    echo "   Connection time: " . round($info['connect_time'], 3) . "s\n";
    echo "   Total time: " . round($end_time - $start_time, 3) . "s\n";
}
echo "\n";

// 3. API Endpoint Test
echo "3. API Endpoint Test:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io/transaction/initialize');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"test":"connection"}');
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/json',
    'Accept: application/json',
    'User-Agent: Lahza-Network-Check/1.0'
));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ FAILED: $error\n";
} else {
    if ($httpCode == 401 || $httpCode == 400) {
        echo "✅ SUCCESS: API endpoint reachable (HTTP $httpCode - expected for unauthorized request)\n";
    } else {
        echo "⚠️  WARNING: Unexpected HTTP $httpCode\n";
    }
    
    if ($response) {
        $decoded = json_decode($response, true);
        if ($decoded && isset($decoded['message'])) {
            echo "   API Response: " . $decoded['message'] . "\n";
        }
    }
}
echo "\n";

// 4. System Information
echo "4. System Information:\n";
echo "   PHP Version: " . PHP_VERSION . "\n";
echo "   cURL Version: " . (function_exists('curl_version') ? curl_version()['version'] : 'Not available') . "\n";
echo "   OpenSSL Version: " . OPENSSL_VERSION_TEXT . "\n";
echo "   Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "   Server IP: " . ($_SERVER['SERVER_ADDR'] ?? 'Unknown') . "\n";
echo "\n";

// 5. Recommendations
echo "5. Recommendations:\n";

if ($ip === $host) {
    echo "❌ DNS resolution failed:\n";
    echo "   → Contact your hosting provider\n";
    echo "   → Ask them to enable DNS resolution for external domains\n";
    echo "   → Request whitelisting of api.lahza.io\n";
} elseif ($error && strpos($error, 'Could not resolve host') !== false) {
    echo "❌ Host resolution error:\n";
    echo "   → Check DNS server configuration\n";
    echo "   → Verify firewall allows DNS queries\n";
    echo "   → Contact hosting support\n";
} elseif ($error) {
    echo "❌ Connection error:\n";
    echo "   → Check firewall settings\n";
    echo "   → Verify outbound HTTPS is allowed\n";
    echo "   → Contact system administrator\n";
} else {
    echo "✅ Network connectivity looks good!\n";
    echo "   → You can proceed with Lahza gateway setup\n";
    echo "   → Run the full gateway test next\n";
}

echo "\n";
echo "=== Next Steps ===\n";
echo "1. If tests failed, contact your hosting provider with this report\n";
echo "2. If tests passed, run: test_gateway.php\n";
echo "3. For detailed connection help, see: CONNECTION_FIX.md\n";
echo "\n";
echo "=== Support Information ===\n";
echo "Show this report to your hosting provider if you need help.\n";
echo "Common request: 'Please enable outbound HTTPS connections and whitelist api.lahza.io'\n";

?>
