# 🔧 Lahza Gateway PowerShell Diagnostic Tools

## Overview

مجموعة شاملة من أدوات PowerShell لتشخيص وحل مشاكل بوابة لحظة للدفع في WHMCS.

---

## 🛠️ Available Tools

### 1. **Quick Check (`quick-check.ps1`)**
**الغرض:** فحص سريع للمشاكل الأساسية

**الاستخدام:**
```powershell
.\quick-check.ps1 yourdomain.com
```

**ما يفحصه:**
- ✅ DNS Resolution (api.lahza.io & your domain)
- ✅ Basic connectivity to Lahza API
- ✅ Webhook URL accessibility
- ✅ SSL certificate validity
- ✅ Gateway files existence
- ✅ Diagnostic tools availability

### 2. **Full Diagnostic (`diagnose.ps1`)**
**الغرض:** تشخيص شامل ومتقدم

**الاستخدام:**
```powershell
# Basic diagnostic
.\diagnose.ps1 -Domain "yourdomain.com"

# With webhook testing
.\diagnose.ps1 -Domain "yourdomain.com" -TestWebhook

# With specific invoice
.\diagnose.ps1 -Domain "yourdomain.com" -InvoiceId "123" -TestWebhook

# Verbose output
.\diagnose.ps1 -Domain "yourdomain.com" -Verbose
```

**المعاملات:**
- `-Domain` (مطلوب): اسم النطاق
- `-InvoiceId` (اختياري): رقم الفاتورة للاختبار
- `-TestWebhook` (اختياري): اختبار وظائف webhook
- `-Verbose` (اختياري): عرض تفاصيل إضافية

### 3. **Webhook Test (`test-webhook.ps1`)**
**الغرض:** اختبار webhook مع سيناريوهات مختلفة

**الاستخدام:**
```powershell
# Test successful payment
.\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "123"

# Test failed payment
.\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "123" -Status "failed"

# Test with secret key
.\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "123" -SecretKey "your_secret_key"

# Test with custom amount
.\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "123" -Amount 100.50
```

**المعاملات:**
- `-Domain` (مطلوب): اسم النطاق
- `-InvoiceId` (مطلوب): رقم الفاتورة
- `-SecretKey` (اختياري): مفتاح التوقيع السري
- `-Status` (اختياري): حالة الدفع (success, failed, pending)
- `-Amount` (اختياري): مبلغ الدفع (افتراضي: 50.00)

---

## 🚀 Quick Start Guide

### Step 1: Download Scripts
قم بتحميل السكريپتات إلى مجلد بوابة لحظة:
```
modules/gateways/lahza/
├── quick-check.ps1
├── diagnose.ps1
├── test-webhook.ps1
└── POWERSHELL_DIAGNOSTIC.md
```

### Step 2: Run Quick Check
```powershell
cd "C:\path\to\your\whmcs\modules\gateways\lahza"
.\quick-check.ps1 yourdomain.com
```

### Step 3: Analyze Results
- ✅ **Green**: All good
- ⚠️ **Yellow**: Warning, may need attention
- ❌ **Red**: Critical issue, needs fixing

### Step 4: Run Full Diagnostic (if needed)
```powershell
.\diagnose.ps1 -Domain "yourdomain.com" -TestWebhook
```

### Step 5: Test Specific Invoice
```powershell
.\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "10" -SecretKey "your_secret"
```

---

## 🔍 Common Diagnostic Scenarios

### Scenario 1: "Invoice Not Updating After Payment"

**Steps:**
1. Run quick check:
   ```powershell
   .\quick-check.ps1 yourdomain.com
   ```

2. If webhook URL is accessible, test webhook:
   ```powershell
   .\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "PROBLEM_INVOICE_ID"
   ```

3. Check results and logs

### Scenario 2: "Cannot Connect to Lahza API"

**Steps:**
1. Run full diagnostic:
   ```powershell
   .\diagnose.ps1 -Domain "yourdomain.com" -Verbose
   ```

2. Check DNS and connectivity results
3. Contact hosting provider if needed

### Scenario 3: "Webhook Not Working"

**Steps:**
1. Test webhook accessibility:
   ```powershell
   .\quick-check.ps1 yourdomain.com
   ```

2. Test webhook functionality:
   ```powershell
   .\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId "123" -SecretKey "your_key"
   ```

3. Check signature and response

---

## 📊 Understanding Results

### DNS Resolution
- **✅ Success**: Domain resolves correctly
- **❌ Failed**: DNS issues, contact hosting provider

### Connectivity Tests
- **✅ HTTP 200**: Service accessible
- **❌ Failed**: Network/firewall issues

### SSL Certificate
- **✅ Valid**: Certificate is current and trusted
- **⚠️ Expired**: Certificate needs renewal
- **❌ Failed**: SSL configuration issues

### Webhook Tests
- **✅ HTTP 200**: Webhook received and processed
- **❌ HTTP 4xx/5xx**: Server or configuration error
- **❌ Connection Failed**: Network or URL issues

---

## 🛠️ Troubleshooting Common Issues

### Issue: "DNS Resolution Failed"
**Cause:** DNS server issues or domain configuration
**Solution:**
1. Check domain DNS settings
2. Try different DNS server (*******)
3. Contact hosting provider

### Issue: "Webhook URL Not Accessible"
**Cause:** File permissions, server configuration, or firewall
**Solution:**
1. Check file exists and has correct permissions
2. Verify web server configuration
3. Check firewall rules

### Issue: "SSL Certificate Problems"
**Cause:** Expired or invalid SSL certificate
**Solution:**
1. Renew SSL certificate
2. Check certificate chain
3. Verify domain matches certificate

### Issue: "Webhook Test Fails"
**Cause:** Incorrect secret key or server processing error
**Solution:**
1. Verify secret key matches Lahza dashboard
2. Check WHMCS gateway configuration
3. Review server error logs

---

## 🔧 Advanced Usage

### Custom Webhook Payload Testing
```powershell
# Test with specific transaction data
$customPayload = @{
    event = "charge.success"
    data = @{
        id = "CUSTOM_TXN_123"
        status = "success"
        amount = 7500  # $75.00 in cents
        metadata = @{
            invoiceid = "456"
            custom_field = "test_value"
        }
    }
} | ConvertTo-Json -Depth 3

# Send custom payload (requires modification of script)
```

### Batch Testing Multiple Invoices
```powershell
$invoices = @("10", "11", "12", "13")
foreach ($invoice in $invoices) {
    Write-Host "Testing invoice $invoice..."
    .\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId $invoice
    Start-Sleep -Seconds 2
}
```

### Automated Monitoring
```powershell
# Run diagnostic every hour
while ($true) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] Running diagnostic..."
    .\quick-check.ps1 yourdomain.com
    Start-Sleep -Seconds 3600  # 1 hour
}
```

---

## 📞 Getting Help

### Self-Diagnosis
1. Run all three scripts in order
2. Document any error messages
3. Check web-based diagnostic tools

### Contact Support
**Include this information:**
- PowerShell script outputs
- Domain name
- WHMCS version
- Hosting provider
- Any error messages

### Web-Based Tools
After PowerShell diagnosis, use web tools:
- **Tools Dashboard**: `https://yourdomain.com/modules/gateways/lahza/tools.html`
- **Webhook Debug**: `https://yourdomain.com/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025`
- **Invoice Test**: `https://yourdomain.com/modules/gateways/lahza/test_invoice_update.php?debug_key=lahza_debug_2025`

---

## 🎯 Best Practices

### Regular Monitoring
- Run quick-check weekly
- Test webhook monthly
- Monitor SSL certificate expiry

### Documentation
- Keep diagnostic outputs
- Document configuration changes
- Maintain contact information

### Security
- Don't share secret keys in logs
- Use secure connections only
- Regularly update credentials

---

**💡 Pro Tip:** Run `quick-check.ps1` first for fast diagnosis, then use specific tools based on results.

**🎉 Most issues can be diagnosed and resolved using these PowerShell tools!**
