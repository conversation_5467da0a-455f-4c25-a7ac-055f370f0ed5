<?php
/**
 * Lahza Payment Gateway Log Viewer
 * 
 * View and analyze Lahza payment gateway logs
 * Access: https://yourdomain.com/modules/gateways/lahza/view_logs.php
 */

// Security check
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/debug_logger.php';

// Check if user is admin (basic security)
$isAdmin = false;
if (isset($_SESSION['adminid']) && !empty($_SESSION['adminid'])) {
    $isAdmin = true;
}

if (!$isAdmin && !isset($_GET['debug_key']) || (isset($_GET['debug_key']) && $_GET['debug_key'] !== 'lahza_debug_2025')) {
    die('Access denied. Admin login required or provide debug key.');
}

$action = $_GET['action'] ?? 'view';
$lines = (int)($_GET['lines'] ?? 100);
$filter = $_GET['filter'] ?? '';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Gateway Logs</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; }
        .controls { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .controls select, .controls input, .controls button { margin: 5px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; }
        .controls button { background: #007bff; color: white; border: none; cursor: pointer; }
        .controls button:hover { background: #0056b3; }
        .log-entry { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; background: #f8f9fa; font-family: monospace; font-size: 12px; }
        .log-entry.error { border-left-color: #dc3545; background: #f8d7da; }
        .log-entry.warning { border-left-color: #ffc107; background: #fff3cd; }
        .log-entry.info { border-left-color: #17a2b8; background: #d1ecf1; }
        .log-entry.debug { border-left-color: #6c757d; background: #e2e3e5; }
        .log-entry.api_request { border-left-color: #28a745; background: #d4edda; }
        .log-entry.api_response { border-left-color: #fd7e14; background: #ffeaa7; }
        .log-entry.webhook { border-left-color: #6f42c1; background: #e2d9f3; }
        .log-entry.payment { border-left-color: #20c997; background: #d1f2eb; }
        .timestamp { color: #6c757d; font-weight: bold; }
        .level { display: inline-block; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; margin-right: 10px; }
        .level.error { background: #dc3545; color: white; }
        .level.warning { background: #ffc107; color: black; }
        .level.info { background: #17a2b8; color: white; }
        .level.debug { background: #6c757d; color: white; }
        .level.api_request { background: #28a745; color: white; }
        .level.api_response { background: #fd7e14; color: white; }
        .level.webhook { background: #6f42c1; color: white; }
        .level.payment { background: #20c997; color: white; }
        .context { margin-top: 5px; padding: 5px; background: rgba(0,0,0,0.05); border-radius: 3px; font-size: 11px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-box { flex: 1; padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 12px; color: #6c757d; }
        .no-logs { text-align: center; padding: 40px; color: #6c757d; }
        .refresh-btn { position: fixed; bottom: 20px; right: 20px; background: #007bff; color: white; border: none; padding: 15px; border-radius: 50%; cursor: pointer; box-shadow: 0 2px 10px rgba(0,0,0,0.2); }
        .refresh-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Lahza Payment Gateway Logs</h1>
            <p>Real-time log monitoring and analysis</p>
        </div>
        
        <div class="content">
            <div class="controls">
                <form method="GET" style="display: inline;">
                    <select name="lines">
                        <option value="50" <?= $lines == 50 ? 'selected' : '' ?>>Last 50 entries</option>
                        <option value="100" <?= $lines == 100 ? 'selected' : '' ?>>Last 100 entries</option>
                        <option value="200" <?= $lines == 200 ? 'selected' : '' ?>>Last 200 entries</option>
                        <option value="500" <?= $lines == 500 ? 'selected' : '' ?>>Last 500 entries</option>
                    </select>
                    
                    <input type="text" name="filter" placeholder="Filter logs..." value="<?= htmlspecialchars($filter) ?>">
                    
                    <?php if (isset($_GET['debug_key'])): ?>
                    <input type="hidden" name="debug_key" value="<?= htmlspecialchars($_GET['debug_key']) ?>">
                    <?php endif; ?>
                    
                    <button type="submit">Filter</button>
                    <button type="button" onclick="location.reload()">Refresh</button>
                </form>
            </div>

<?php

// Get logs
$logs = LahzaDebugLogger::getRecentLogs($lines);

// Filter logs if needed
if (!empty($filter)) {
    $logs = array_filter($logs, function($log) use ($filter) {
        return stripos($log, $filter) !== false;
    });
}

// Parse and analyze logs
$stats = [
    'total' => count($logs),
    'errors' => 0,
    'warnings' => 0,
    'api_requests' => 0,
    'webhooks' => 0,
    'payments' => 0
];

foreach ($logs as $log) {
    if (strpos($log, '[ERROR]') !== false) $stats['errors']++;
    if (strpos($log, '[WARNING]') !== false) $stats['warnings']++;
    if (strpos($log, '[API_REQUEST]') !== false) $stats['api_requests']++;
    if (strpos($log, '[WEBHOOK]') !== false) $stats['webhooks']++;
    if (strpos($log, '[PAYMENT]') !== false) $stats['payments']++;
}

?>

            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number"><?= $stats['total'] ?></div>
                    <div class="stat-label">Total Entries</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number" style="color: #dc3545;"><?= $stats['errors'] ?></div>
                    <div class="stat-label">Errors</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number" style="color: #ffc107;"><?= $stats['warnings'] ?></div>
                    <div class="stat-label">Warnings</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number" style="color: #28a745;"><?= $stats['api_requests'] ?></div>
                    <div class="stat-label">API Requests</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number" style="color: #6f42c1;"><?= $stats['webhooks'] ?></div>
                    <div class="stat-label">Webhooks</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number" style="color: #20c997;"><?= $stats['payments'] ?></div>
                    <div class="stat-label">Payments</div>
                </div>
            </div>

            <div class="logs">
                <?php if (empty($logs)): ?>
                    <div class="no-logs">
                        <h3>No logs found</h3>
                        <p>No log entries match your criteria. Try adjusting the filter or check if logging is enabled.</p>
                    </div>
                <?php else: ?>
                    <?php foreach (array_reverse($logs) as $log): ?>
                        <?php
                        // Parse log entry
                        if (preg_match('/\[(.*?)\] \[(.*?)\] (.*?)( \| Context: (.*))?$/', $log, $matches)) {
                            $timestamp = $matches[1];
                            $level = strtolower($matches[2]);
                            $message = $matches[3];
                            $context = isset($matches[5]) ? $matches[5] : '';
                        } else {
                            $timestamp = '';
                            $level = 'info';
                            $message = $log;
                            $context = '';
                        }
                        ?>
                        <div class="log-entry <?= $level ?>">
                            <div>
                                <span class="timestamp"><?= htmlspecialchars($timestamp) ?></span>
                                <span class="level <?= $level ?>"><?= strtoupper($level) ?></span>
                                <span class="message"><?= htmlspecialchars($message) ?></span>
                            </div>
                            <?php if (!empty($context)): ?>
                                <div class="context">
                                    <strong>Context:</strong> <?= htmlspecialchars($context) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="location.reload()" title="Refresh logs">
        🔄
    </button>

    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // Scroll to bottom on load
        window.addEventListener('load', function() {
            window.scrollTo(0, document.body.scrollHeight);
        });
    </script>
</body>
</html>
