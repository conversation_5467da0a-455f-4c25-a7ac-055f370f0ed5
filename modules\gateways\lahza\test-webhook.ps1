# Lahza Webhook Test Script
# Test webhook functionality with different payment scenarios
# Usage: .\test-webhook.ps1 -Domain "yourdomain.com" -InvoiceId 123 [-<PERSON><PERSON>ey "your_secret"]

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$InvoiceId,
    
    [Parameter(Mandatory=$false)]
    [string]$SecretKey = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("success", "failed", "pending")]
    [string]$Status = "success",
    
    [Parameter(Mandatory=$false)]
    [decimal]$Amount = 50.00
)

# Function to generate HMAC signature
function Get-HMACSignature {
    param(
        [string]$Data,
        [string]$Key
    )
    
    if ([string]::IsNullOrEmpty($Key)) {
        return "test_signature_no_key_provided"
    }
    
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = [System.Text.Encoding]::UTF8.GetBytes($Key)
    $hash = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($Data))
    return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
}

# Function to create webhook payload
function New-WebhookPayload {
    param(
        [string]$Status,
        [string]$InvoiceId,
        [decimal]$Amount
    )
    
    $transactionId = "TEST_$(Get-Date -Format 'yyyyMMddHHmmss')_$InvoiceId"
    $reference = "INV-$InvoiceId-$(Get-Date -Format 'yyyyMMddHHmmss')-$(Get-Random -Maximum 9999)"
    
    $eventType = switch ($Status) {
        "success" { "charge.success" }
        "failed" { "charge.failed" }
        "pending" { "charge.pending" }
        default { "charge.success" }
    }
    
    $payload = @{
        event = $eventType
        data = @{
            id = $transactionId
            status = $Status
            amount = [int]($Amount * 100)  # Convert to cents
            currency = "USD"
            reference = $reference
            gateway_response = switch ($Status) {
                "success" { "Payment completed successfully" }
                "failed" { "Payment declined by bank" }
                "pending" { "Payment pending completion" }
                default { "Payment processed" }
            }
            paid_at = if ($Status -eq "success") { (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ") } else { $null }
            created_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            channel = "card"
            fees = [int]($Amount * 0.03 * 100)  # 3% fee in cents
            metadata = @{
                invoiceid = $InvoiceId
                clientid = "1"
                description = "Test Payment via PowerShell"
                company = "Test Company"
            }
            customer = @{
                id = 1
                email = "<EMAIL>"
                phone = "+************"
            }
        }
    }
    
    return $payload | ConvertTo-Json -Depth 4
}

# Function to send webhook
function Send-Webhook {
    param(
        [string]$WebhookUrl,
        [string]$Payload,
        [string]$Signature
    )
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-Lahza-Signature' = $Signature
        'User-Agent' = 'Lahza-PowerShell-Test/1.0'
    }
    
    try {
        $response = Invoke-WebRequest -Uri $WebhookUrl -Method Post -Body $Payload -Headers $headers -TimeoutSec 30
        
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            StatusDescription = $response.StatusDescription
            Content = $response.Content
            Headers = $response.Headers
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
            StatusDescription = "Failed"
            Content = $null
            Headers = $null
            Error = $_.Exception.Message
        }
    }
}

# Main execution
Write-Host "🧪 Lahza Webhook Test" -ForegroundColor Cyan
Write-Host "=" * 30 -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 Test Parameters:" -ForegroundColor Yellow
Write-Host "   Domain: $Domain" -ForegroundColor White
Write-Host "   Invoice ID: $InvoiceId" -ForegroundColor White
Write-Host "   Status: $Status" -ForegroundColor White
Write-Host "   Amount: $Amount USD" -ForegroundColor White
Write-Host "   Secret Key: $(if ($SecretKey) { 'Provided' } else { 'Not provided (using test signature)' })" -ForegroundColor White
Write-Host ""

# Generate webhook URL
$webhookUrl = "https://$Domain/modules/gateways/callback/lahza.php"
Write-Host "🎯 Target Webhook URL:" -ForegroundColor Yellow
Write-Host "   $webhookUrl" -ForegroundColor White
Write-Host ""

# Test webhook URL accessibility first
Write-Host "🔍 Testing webhook URL accessibility..." -ForegroundColor Yellow
try {
    $accessTest = Invoke-WebRequest -Uri $webhookUrl -Method Head -TimeoutSec 10
    Write-Host "   ✅ Webhook URL accessible: HTTP $($accessTest.StatusCode)" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ Webhook URL not accessible: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   ⚠️  Continuing with test anyway..." -ForegroundColor Yellow
}
Write-Host ""

# Generate payload
Write-Host "📦 Generating webhook payload..." -ForegroundColor Yellow
$payload = New-WebhookPayload -Status $Status -InvoiceId $InvoiceId -Amount $Amount

Write-Host "   ✅ Payload generated successfully" -ForegroundColor Green
Write-Host ""

# Generate signature
Write-Host "🔐 Generating HMAC signature..." -ForegroundColor Yellow
$signature = Get-HMACSignature -Data $payload -Key $SecretKey

if ($SecretKey) {
    Write-Host "   ✅ HMAC signature generated with provided secret key" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  Using test signature (no secret key provided)" -ForegroundColor Yellow
}
Write-Host ""

# Send webhook
Write-Host "🚀 Sending webhook..." -ForegroundColor Yellow
$result = Send-Webhook -WebhookUrl $webhookUrl -Payload $payload -Signature $signature

# Display results
Write-Host ""
Write-Host "📊 WEBHOOK TEST RESULTS" -ForegroundColor Cyan
Write-Host "=" * 25 -ForegroundColor Cyan
Write-Host ""

if ($result.Success) {
    Write-Host "✅ Webhook sent successfully!" -ForegroundColor Green
    Write-Host "   Status Code: $($result.StatusCode)" -ForegroundColor White
    Write-Host "   Status: $($result.StatusDescription)" -ForegroundColor White
    
    if ($result.Content) {
        Write-Host "   Response:" -ForegroundColor White
        Write-Host "   $($result.Content)" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ Webhook failed!" -ForegroundColor Red
    Write-Host "   Error: $($result.Error)" -ForegroundColor White
    
    if ($result.StatusCode -gt 0) {
        Write-Host "   HTTP Status: $($result.StatusCode)" -ForegroundColor White
    }
}

Write-Host ""

# Show payload for reference
Write-Host "📄 Payload sent:" -ForegroundColor Yellow
Write-Host $payload -ForegroundColor Gray
Write-Host ""

# Show signature for reference
Write-Host "🔐 Signature sent:" -ForegroundColor Yellow
Write-Host $signature -ForegroundColor Gray
Write-Host ""

# Recommendations
Write-Host "💡 Next Steps:" -ForegroundColor Cyan
Write-Host ""

if ($result.Success) {
    Write-Host "1. Check WHMCS invoice $InvoiceId to see if status updated" -ForegroundColor White
    Write-Host "2. Review WHMCS Gateway Logs for processing details" -ForegroundColor White
    Write-Host "3. Check webhook logs: https://$Domain/modules/gateways/lahza/view_logs.php?debug_key=lahza_debug_2025" -ForegroundColor White
} else {
    Write-Host "1. Verify webhook URL is accessible" -ForegroundColor White
    Write-Host "2. Check server error logs" -ForegroundColor White
    Write-Host "3. Ensure Lahza gateway is properly configured in WHMCS" -ForegroundColor White
    Write-Host "4. Run diagnostic: .\diagnose.ps1 -Domain $Domain" -ForegroundColor White
}

Write-Host ""
Write-Host "🔗 Useful Links:" -ForegroundColor Cyan
Write-Host "   • Debug Webhook: https://$Domain/modules/gateways/lahza/debug_webhook.php?debug_key=lahza_debug_2025" -ForegroundColor Gray
Write-Host "   • Test Invoice: https://$Domain/modules/gateways/lahza/test_invoice_update.php?debug_key=lahza_debug_2025" -ForegroundColor Gray
Write-Host "   • View Logs: https://$Domain/modules/gateways/lahza/view_logs.php?debug_key=lahza_debug_2025" -ForegroundColor Gray

Write-Host ""
Write-Host "Test completed at $(Get-Date)" -ForegroundColor Blue
