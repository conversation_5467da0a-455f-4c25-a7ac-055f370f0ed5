<?php
/**
 * Lahza Payment Gateway Quick Setup
 * 
 * Quick setup wizard for Lahza payment gateway
 * Access: https://yourdomain.com/modules/gateways/lahza/quick_setup.php
 */

// Security check
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['adminid']) && !empty($_SESSION['adminid'])) {
    $isAdmin = true;
}

if (!$isAdmin) {
    die('Access denied. Admin login required.');
}

$step = (int)($_GET['step'] ?? 1);
$message = '';
$messageType = '';

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 2:
            // Activate gateway
            try {
                $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
                $stmt = $pdo->prepare("UPDATE tblpaymentgateways SET setting = 'on' WHERE gateway = 'lahza' AND setting = 'type'");
                $stmt->execute();
                
                $message = "Gateway activated successfully!";
                $messageType = "success";
                $step = 3;
            } catch (Exception $e) {
                $message = "Error activating gateway: " . $e->getMessage();
                $messageType = "error";
            }
            break;
            
        case 3:
            // Save configuration
            try {
                $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
                
                $settings = [
                    'publicKey' => $_POST['publicKey'] ?? '',
                    'secretKey' => $_POST['secretKey'] ?? '',
                    'testMode' => isset($_POST['testMode']) ? 'on' : '',
                    'defaultCurrency' => $_POST['defaultCurrency'] ?? 'USD',
                    'paymentChannels' => $_POST['paymentChannels'] ?? 'card,bank,mobile_money'
                ];
                
                foreach ($settings as $setting => $value) {
                    $stmt = $pdo->prepare("
                        INSERT INTO tblpaymentgateways (gateway, setting, value) 
                        VALUES ('lahza', ?, ?) 
                        ON DUPLICATE KEY UPDATE value = ?
                    ");
                    $stmt->execute([$setting, $value, $value]);
                }
                
                $message = "Configuration saved successfully!";
                $messageType = "success";
                $step = 4;
            } catch (Exception $e) {
                $message = "Error saving configuration: " . $e->getMessage();
                $messageType = "error";
            }
            break;
    }
}

// Get current configuration
$currentConfig = [];
try {
    $gatewayParams = getGatewayVariables('lahza');
    $currentConfig = $gatewayParams ?: [];
} catch (Exception $e) {
    // Gateway not configured yet
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Gateway Quick Setup</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { padding: 30px; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 30px; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #e9ecef; color: #6c757d; display: flex; align-items: center; justify-content: center; margin: 0 10px; font-weight: bold; }
        .step.active { background: #007bff; color: white; }
        .step.completed { background: #28a745; color: white; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0,123,255,0.25); }
        .btn { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .checkbox-group { display: flex; align-items: center; }
        .checkbox-group input { width: auto; margin-right: 10px; }
        .info-box { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .webhook-url { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all; }
        .progress { width: 100%; height: 6px; background: #e9ecef; border-radius: 3px; margin-bottom: 30px; }
        .progress-bar { height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 3px; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Lahza Gateway Quick Setup</h1>
            <p>Get your Lahza payment gateway up and running in minutes</p>
        </div>
        
        <div class="content">
            <div class="progress">
                <div class="progress-bar" style="width: <?= ($step / 4) * 100 ?>%"></div>
            </div>
            
            <div class="step-indicator">
                <div class="step <?= $step >= 1 ? 'active' : '' ?> <?= $step > 1 ? 'completed' : '' ?>">1</div>
                <div class="step <?= $step >= 2 ? 'active' : '' ?> <?= $step > 2 ? 'completed' : '' ?>">2</div>
                <div class="step <?= $step >= 3 ? 'active' : '' ?> <?= $step > 3 ? 'completed' : '' ?>">3</div>
                <div class="step <?= $step >= 4 ? 'active' : '' ?>">4</div>
            </div>

            <?php if ($message): ?>
                <div class="alert <?= $messageType ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <h2>Step 1: Prerequisites Check</h2>
                <div class="info-box">
                    <h3>Before we begin, make sure you have:</h3>
                    <ul>
                        <li>✅ Active Lahza merchant account</li>
                        <li>✅ Lahza API keys (public and secret)</li>
                        <li>✅ WHMCS admin access</li>
                        <li>✅ SSL certificate installed</li>
                    </ul>
                </div>
                
                <p>This wizard will help you:</p>
                <ul>
                    <li>Activate the Lahza payment gateway</li>
                    <li>Configure your API keys</li>
                    <li>Set up webhook URL</li>
                    <li>Test the integration</li>
                </ul>
                
                <a href="?step=2" class="btn">Start Setup</a>

            <?php elseif ($step == 2): ?>
                <h2>Step 2: Activate Gateway</h2>
                <div class="info-box">
                    <p>We'll activate the Lahza payment gateway in your WHMCS installation.</p>
                </div>
                
                <form method="POST">
                    <button type="submit" class="btn">Activate Lahza Gateway</button>
                </form>

            <?php elseif ($step == 3): ?>
                <h2>Step 3: Configure Settings</h2>
                <div class="info-box">
                    <p>Enter your Lahza API credentials and configure the gateway settings.</p>
                </div>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="publicKey">Public Key *</label>
                        <input type="text" id="publicKey" name="publicKey" value="<?= htmlspecialchars($currentConfig['publicKey'] ?? '') ?>" required placeholder="pk_test_... or pk_live_...">
                    </div>
                    
                    <div class="form-group">
                        <label for="secretKey">Secret Key *</label>
                        <input type="password" id="secretKey" name="secretKey" value="<?= htmlspecialchars($currentConfig['secretKey'] ?? '') ?>" required placeholder="sk_test_... or sk_live_...">
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="testMode" name="testMode" <?= (!empty($currentConfig['testMode'])) ? 'checked' : '' ?>>
                            <label for="testMode">Enable Test Mode (recommended for initial setup)</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="defaultCurrency">Default Currency</label>
                        <select id="defaultCurrency" name="defaultCurrency">
                            <option value="USD" <?= ($currentConfig['defaultCurrency'] ?? 'USD') == 'USD' ? 'selected' : '' ?>>US Dollar (USD)</option>
                            <option value="ILS" <?= ($currentConfig['defaultCurrency'] ?? '') == 'ILS' ? 'selected' : '' ?>>Israeli Shekel (ILS)</option>
                            <option value="JOD" <?= ($currentConfig['defaultCurrency'] ?? '') == 'JOD' ? 'selected' : '' ?>>Jordanian Dinar (JOD)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="paymentChannels">Payment Channels</label>
                        <input type="text" id="paymentChannels" name="paymentChannels" value="<?= htmlspecialchars($currentConfig['paymentChannels'] ?? 'card,bank,mobile_money') ?>" placeholder="card,bank,mobile_money">
                        <small>Comma-separated list: card, bank, ussd, qr, mobile_money, bank_transfer</small>
                    </div>
                    
                    <button type="submit" class="btn">Save Configuration</button>
                    <a href="?step=2" class="btn btn-secondary">Back</a>
                </form>

            <?php elseif ($step == 4): ?>
                <h2>Step 4: Setup Complete!</h2>
                <div class="alert success">
                    <strong>Congratulations!</strong> Your Lahza payment gateway has been configured successfully.
                </div>
                
                <div class="info-box">
                    <h3>📋 Next Steps:</h3>
                    <ol>
                        <li><strong>Configure Webhook URL in Lahza Dashboard:</strong>
                            <div class="webhook-url">
                                https://<?= $_SERVER['HTTP_HOST'] ?>/modules/gateways/callback/lahza.php
                            </div>
                        </li>
                        <li><strong>Test the Integration:</strong>
                            <a href="test_gateway.php" target="_blank" class="btn">Run Test Script</a>
                        </li>
                        <li><strong>Process a Test Payment</strong> (if test mode is enabled)</li>
                        <li><strong>Monitor Logs:</strong>
                            <a href="view_logs.php?debug_key=lahza_debug_2025" target="_blank" class="btn">View Logs</a>
                        </li>
                    </ol>
                </div>
                
                <div class="info-box">
                    <h3>🔧 Useful Links:</h3>
                    <ul>
                        <li><a href="../../../admin/configgateways.php" target="_blank">WHMCS Payment Gateway Settings</a></li>
                        <li><a href="../../../admin/logs.php?type=gateway" target="_blank">WHMCS Gateway Logs</a></li>
                        <li><a href="https://docs.lahza.io/" target="_blank">Lahza Documentation</a></li>
                    </ul>
                </div>
                
                <div class="alert info">
                    <strong>Security Note:</strong> For security reasons, consider deleting the setup files (quick_setup.php, test_gateway.php) after completing the setup.
                </div>

            <?php endif; ?>
        </div>
    </div>
</body>
</html>
