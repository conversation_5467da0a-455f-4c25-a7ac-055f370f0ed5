{"schema": "1.0", "type": "gateway", "name": "la<PERSON>za", "display_name": "Lahza Payment Gateway", "version": "1.3.0", "description": "Accept payments through Lahza payment gateway with support for multiple payment methods including cards, bank transfers, and mobile money.", "author": {"name": "WIDDX", "url": "https://widdx.com", "email": "<EMAIL>"}, "website": "https://lahza.io", "documentation": "https://docs.lahza.io", "support": "https://widdx.com/support", "logo": "logo.png", "category": "Payment Gateways", "tags": ["payment", "gateway", "la<PERSON>za", "middle-east", "cards", "mobile-money", "bank-transfer"], "requirements": {"whmcs": ">=7.0", "php": ">=7.0", "extensions": ["curl", "json", "openssl"]}, "features": ["Third Party Gateway", "Refunds", "Webhooks", "Multiple Currencies", "Multiple Payment Methods", "Test Mode", "Secure Transactions"], "supported_currencies": ["USD", "ILS", "JOD"], "supported_countries": ["IL", "JO", "PS", "US", "GB"], "configuration": {"publicKey": {"type": "text", "required": true, "description": "Your <PERSON><PERSON>za public key"}, "secretKey": {"type": "password", "required": true, "description": "Your <PERSON><PERSON><PERSON> secret key"}, "testMode": {"type": "yesno", "required": false, "default": false, "description": "Enable test mode"}, "defaultCurrency": {"type": "dropdown", "required": false, "default": "USD", "options": {"USD": "US Dollar", "ILS": "Israeli Shekel", "JOD": "<PERSON><PERSON>"}, "description": "Default currency for transactions"}, "paymentChannels": {"type": "text", "required": false, "default": "card,bank,mobile_money", "description": "Comma-separated list of payment channels"}}, "changelog": [{"version": "1.3.0", "date": "2025-01-16", "changes": ["Fixed critical invoice update issue after successful payments", "Added comprehensive webhook debugging tools", "Created invoice update test tool for direct payment testing", "Enhanced webhook reception and processing diagnostics", "Added invoice status verification before payment addition", "Improved duplicate payment prevention", "Created detailed invoice update fix guide", "Added webhook accessibility testing", "Enhanced gateway configuration validation", "Updated tools dashboard with invoice debugging capabilities", "Added emergency manual payment workaround documentation", "Improved error handling for webhook signature verification"]}, {"version": "1.2.0", "date": "2025-01-16", "changes": ["Enhanced payment status handling with comprehensive status mapping", "Added support for failed, pending, and unknown payment statuses", "Implemented webhook event type detection and processing", "Added webhook test tool for testing different payment scenarios", "Created comprehensive payment statuses documentation", "Enhanced logging for different payment status outcomes", "Added support for refund webhook events", "Improved webhook signature verification with multiple header support", "Added detailed payment status guide and troubleshooting", "Updated tools dashboard with webhook testing capabilities"]}, {"version": "1.1.0", "date": "2025-01-16", "changes": ["Fixed client ID extraction from WHMCS parameters", "Improved phone number validation and cleaning", "Enhanced webhook signature verification with multiple header support", "Better error handling and logging throughout the system", "Improved API response parsing with multiple fallback options", "Enhanced callback data extraction with multiple structure support", "Added comprehensive debugging and logging features", "Fixed API endpoint selection for test vs production modes", "Improved transaction reference generation and validation", "Added test script for easy troubleshooting", "Added debug logger for enhanced monitoring", "Added log viewer for real-time log analysis", "Added quick setup wizard for easy installation", "Updated documentation with troubleshooting guide"]}, {"version": "1.0.0", "date": "2025-01-15", "changes": ["Initial release", "Support for Lahza payment gateway", "Multiple payment methods support", "Webhook integration", "Refund functionality", "Test mode support", "Multi-currency support"]}]}