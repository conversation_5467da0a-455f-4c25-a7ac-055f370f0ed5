<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lahza Gateway Tools</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 40px;
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .tool-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        .tool-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.3em;
        }
        .tool-card p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.5;
        }
        .tool-card .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        .tool-card .btn:hover {
            background: #5a6fd8;
        }
        .tool-card.urgent {
            border-left: 4px solid #dc3545;
        }
        .tool-card.setup {
            border-left: 4px solid #28a745;
        }
        .tool-card.diagnostic {
            border-left: 4px solid #ffc107;
        }
        .tool-card.monitoring {
            border-left: 4px solid #17a2b8;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ Lahza Gateway Tools</h1>
            <p>Diagnostic and setup tools for Lahza Payment Gateway</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ Security Notice:</strong> These tools are for setup and troubleshooting only. 
                Delete them after completing your setup for security reasons.
            </div>

            <div class="info">
                <strong>💡 Common Issue:</strong> If you're getting "Could not resolve host: api.lahza.io" error, 
                start with the Network Check tool below.
            </div>

            <div class="tools-grid">
                <div class="tool-card urgent" onclick="window.open('network_check.php', '_blank')">
                    <h3>🔗 Network Check</h3>
                    <p>Quick network diagnostic for connection issues. Start here if you're getting DNS or connection errors.</p>
                    <a href="network_check.php" target="_blank" class="btn">Run Network Check</a>
                </div>

                <div class="tool-card setup" onclick="window.open('quick_setup.php', '_blank')">
                    <h3>🚀 Quick Setup Wizard</h3>
                    <p>Step-by-step setup wizard to configure your Lahza payment gateway in minutes.</p>
                    <a href="quick_setup.php" target="_blank" class="btn">Start Setup</a>
                </div>

                <div class="tool-card diagnostic" onclick="window.open('test_gateway.php', '_blank')">
                    <h3>🔧 Gateway Test</h3>
                    <p>Comprehensive test of gateway configuration, API connectivity, and all components.</p>
                    <a href="test_gateway.php" target="_blank" class="btn">Test Gateway</a>
                </div>

                <div class="tool-card diagnostic" onclick="window.open('test_connection.php', '_blank')">
                    <h3>🌐 Connection Test</h3>
                    <p>Detailed connection test with SSL verification, DNS resolution, and API endpoint testing.</p>
                    <a href="test_connection.php" target="_blank" class="btn">Test Connection</a>
                </div>

                <div class="tool-card monitoring" onclick="window.open('view_logs.php?debug_key=lahza_debug_2025', '_blank')">
                    <h3>📊 Log Viewer</h3>
                    <p>Real-time log monitoring with statistics, filtering, and auto-refresh capabilities.</p>
                    <a href="view_logs.php?debug_key=lahza_debug_2025" target="_blank" class="btn">View Logs</a>
                </div>

                <div class="tool-card diagnostic" onclick="window.open('test_webhook.php?debug_key=lahza_debug_2025', '_blank')">
                    <h3>🧪 Webhook Test</h3>
                    <p>Test different payment statuses and webhook scenarios (success, failed, pending).</p>
                    <a href="test_webhook.php?debug_key=lahza_debug_2025" target="_blank" class="btn">Test Webhooks</a>
                </div>

                <div class="tool-card setup">
                    <h3>📚 Documentation</h3>
                    <p>Comprehensive guides, troubleshooting tips, and payment status documentation.</p>
                    <div style="margin-top: 10px;">
                        <a href="QUICK_START.md" target="_blank" class="btn" style="margin-right: 5px;">Quick Start</a>
                        <a href="CONNECTION_FIX.md" target="_blank" class="btn" style="margin-right: 5px;">Connection Fix</a>
                        <a href="PAYMENT_STATUSES.md" target="_blank" class="btn">Payment Statuses</a>
                    </div>
                </div>
            </div>

            <div class="info">
                <h3>🔄 Typical Workflow:</h3>
                <ol>
                    <li><strong>Network Issues?</strong> → Run Network Check first</li>
                    <li><strong>New Setup?</strong> → Use Quick Setup Wizard</li>
                    <li><strong>Testing?</strong> → Run Gateway Test</li>
                    <li><strong>Webhook Issues?</strong> → Use Webhook Test</li>
                    <li><strong>Debugging?</strong> → Check Log Viewer</li>
                    <li><strong>Need Help?</strong> → Read Documentation</li>
                </ol>
            </div>

            <div class="warning">
                <h3>🚨 Common Error Solutions:</h3>
                <ul>
                    <li><strong>"Could not resolve host"</strong> → Run Network Check, contact hosting provider</li>
                    <li><strong>"Payment initialization failed"</strong> → Check API keys in Gateway Test</li>
                    <li><strong>"Webhook not working"</strong> → Verify webhook URL in Connection Test</li>
                    <li><strong>"Invoice not marked as paid"</strong> → Test webhook statuses with Webhook Test</li>
                    <li><strong>"Payment status unknown"</strong> → Check Payment Statuses documentation</li>
                    <li><strong>"Module not activated"</strong> → Use Quick Setup Wizard</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>Lahza Payment Gateway v1.1.0 | Developed by WIDDX</p>
            <p><small>Remember to delete these tools after setup for security</small></p>
        </div>
    </div>

    <script>
        // Add click handlers for better UX
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A') {
                    const link = this.querySelector('a');
                    if (link) {
                        window.open(link.href, '_blank');
                    }
                }
            });
        });
    </script>
</body>
</html>
