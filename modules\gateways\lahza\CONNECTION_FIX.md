# 🔧 Lahza API Connection Fix Guide

## ❌ Error: "Could not resolve host: api.lahza.io"

This error indicates a DNS resolution problem. Here are the solutions:

---

## 🚀 Quick Fixes

### 1. **Test Connection First**
```
https://yourdomain.com/modules/gateways/lahza/test_connection.php
```

### 2. **Check DNS Resolution**
Run this command on your server:
```bash
nslookup api.lahza.io
```

Expected result:
```
Server: *******
Address: *******#53

Non-authoritative answer:
Name: api.lahza.io
Address: [IP_ADDRESS]
```

---

## 🔍 Common Causes & Solutions

### **1. DNS Server Issues**

**Problem:** Your server can't resolve `api.lahza.io`

**Solutions:**
- Contact your hosting provider
- Check DNS settings in server configuration
- Try using public DNS servers (*******, *******)

### **2. Firewall Blocking**

**Problem:** Firewall blocking outbound connections

**Solutions:**
- Whitelist `api.lahza.io` in firewall
- Allow HTTPS (port 443) outbound connections
- Contact system administrator

### **3. Network Configuration**

**Problem:** Server network misconfiguration

**Solutions:**
- Check network interface configuration
- Verify internet connectivity
- Test with other external APIs

### **4. Hosting Provider Restrictions**

**Problem:** Hosting provider blocking external API calls

**Solutions:**
- Contact hosting support
- Request API access whitelist
- Consider upgrading hosting plan

---

## 🛠️ Technical Solutions

### **For cPanel/WHM Users:**
1. Go to **WHM → Service Configuration → Exim Configuration Manager**
2. Enable **Allow outbound connections**
3. Restart services

### **For VPS/Dedicated Servers:**
```bash
# Test DNS resolution
dig api.lahza.io

# Test HTTP connectivity
curl -I https://api.lahza.io

# Check firewall rules
iptables -L OUTPUT
```

### **For Shared Hosting:**
- Contact support to enable external API access
- Request whitelisting of `api.lahza.io`
- Verify cURL is enabled

---

## 📋 Verification Steps

### **1. DNS Test**
```php
<?php
$host = 'api.lahza.io';
$ip = gethostbyname($host);
echo ($ip !== $host) ? "✅ DNS OK: $ip" : "❌ DNS Failed";
?>
```

### **2. Connection Test**
```php
<?php
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_NOBODY, true);

$result = curl_exec($ch);
$error = curl_error($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($error) {
    echo "❌ Error: $error";
} else {
    echo "✅ Connection OK: HTTP $httpCode";
}
?>
```

---

## 🏥 Emergency Workarounds

### **1. Use IP Address (Temporary)**
If DNS fails, you can temporarily use the IP address:

```php
// In LahzaApiHandler.php, temporarily replace:
$this->apiBaseUrl = 'https://api.lahza.io';

// With (get current IP first):
$this->apiBaseUrl = 'https://[IP_ADDRESS]';
```

**⚠️ Warning:** This is temporary only. IP addresses can change.

### **2. Proxy Configuration**
If behind a corporate proxy:

```php
// Add to cURL options in LahzaApiHandler.php:
CURLOPT_PROXY => 'proxy.company.com:8080',
CURLOPT_PROXYUSERPWD => 'username:password',
```

---

## 📞 Getting Help

### **Contact Your Hosting Provider**
Ask them to:
1. ✅ Enable outbound HTTPS connections
2. ✅ Whitelist `api.lahza.io`
3. ✅ Check DNS resolution
4. ✅ Verify firewall settings

### **Information to Provide:**
- Error message: "Could not resolve host: api.lahza.io"
- Server type: Shared/VPS/Dedicated
- Control panel: cPanel/Plesk/Other
- PHP version and cURL version

### **Test Commands for Support:**
```bash
# DNS test
nslookup api.lahza.io

# Connection test
curl -I https://api.lahza.io

# Traceroute
traceroute api.lahza.io
```

---

## ✅ Verification Checklist

After applying fixes:

- [ ] DNS resolves `api.lahza.io`
- [ ] HTTPS connection works
- [ ] cURL can reach the API
- [ ] Firewall allows outbound connections
- [ ] Test script shows success
- [ ] Gateway test passes

---

## 🔄 Next Steps

1. **Run Connection Test:**
   ```
   https://yourdomain.com/modules/gateways/lahza/test_connection.php
   ```

2. **Run Gateway Test:**
   ```
   https://yourdomain.com/modules/gateways/lahza/test_gateway.php
   ```

3. **Test Payment Flow:**
   - Create test invoice
   - Try payment with test mode enabled
   - Check logs for any remaining issues

---

## 📚 Additional Resources

- **Lahza API Documentation:** https://docs.lahza.io/
- **WHMCS Gateway Logs:** Admin → Utilities → Logs → Gateway Log
- **Server Error Logs:** Check your hosting control panel

---

**🎯 Most Common Solution:** Contact your hosting provider to enable outbound HTTPS connections and whitelist `api.lahza.io`

*This issue is typically resolved within 24 hours by hosting support.*
