<?php
/**
 * Developed by WIDDX (https://widdx.com)
 * © 2025 All Rights Reserved
 * 
 * Lahza Payment Gateway Callback Handler for WHMCS
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';
require_once __DIR__ . '/../lahza/lib/LahzaApiHandler.php';

// Import Capsule for database access
use WHMCS\Database\Capsule;

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

// Initialize response
$response = array('status' => 'error', 'message' => 'Invalid request');

try {
    // Get request method
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    
    if ($requestMethod === 'POST') {
        // Handle webhook notification
        handleWebhook($gatewayParams);
    } elseif ($requestMethod === 'GET') {
        // Handle return from payment page
        handleReturn($gatewayParams);
    } else {
        throw new Exception('Invalid request method');
    }

} catch (Exception $e) {
    // Log error
    logTransaction($gatewayParams['name'], array('error' => $e->getMessage()), 'Callback Error: ' . $e->getMessage());
    
    // Return error response for webhooks
    if ($requestMethod === 'POST') {
        http_response_code(400);
        echo json_encode(array('status' => 'error', 'message' => $e->getMessage()));
    } else {
        // Redirect to invoice for GET requests
        if (isset($_GET['reference'])) {
            $reference = $_GET['reference'];
            $invoiceId = null;
            
            // Support multiple INV formats
            if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
                // New unique format: INV-123-1234567890-abcd1234
                $invoiceId = (int)$matches[1];
            } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
                // Simple format: INV-123
                $invoiceId = (int)$matches[1];
            } elseif (is_numeric($reference)) {
                // Pure numeric format (legacy)
                $invoiceId = (int)$reference;
            }
            
            if ($invoiceId) {
                global $CONFIG;
                $systemUrl = rtrim($CONFIG['SystemURL'], '/');
                if (empty($systemUrl)) {
                    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
                    $systemUrl = $protocol . $_SERVER['HTTP_HOST'];
                }
                header('Location: ' . $systemUrl . '/viewinvoice.php?id=' . $invoiceId);
                exit;
            }
        }
        die('Payment processing error. Please contact support.');
    }
}

/**
 * Handle webhook notifications from Lahza
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleWebhook($gatewayParams)
{
    global $gatewayModuleName;
    
    // Get raw POST data
    $rawPayload = file_get_contents('php://input');
    
    // Log raw payload for debugging
    logTransaction($gatewayParams['name'], array('raw_payload' => $rawPayload), 'Raw Webhook Payload Received');
    
    if (empty($rawPayload)) {
        throw new Exception('Empty webhook payload');
    }
    
    $event = json_decode($rawPayload, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON payload: ' . json_last_error_msg());
    }
    
    // Log parsed event for debugging
    logTransaction($gatewayParams['name'], $event, 'Parsed Webhook Event');
    
    // Get signature from headers
    $signature = isset($_SERVER['HTTP_X_LAHZA_SIGNATURE']) ? $_SERVER['HTTP_X_LAHZA_SIGNATURE'] : '';
    
    if (empty($signature)) {
        throw new Exception('Missing webhook signature');
    }
    
    // Verify signature
    $calculatedSignature = hash_hmac('sha256', $rawPayload, $gatewayParams['secretKey']);
    
    if (!hash_equals($signature, $calculatedSignature)) {
        logTransaction($gatewayParams['name'], array(
            'received_signature' => $signature,
            'calculated_signature' => $calculatedSignature
        ), 'Signature Mismatch');
        throw new Exception('Invalid webhook signature');
    }
    
    // Extract data from webhook with error checking
    if (!isset($event['data']) || !is_array($event['data'])) {
        throw new Exception('Invalid webhook data structure');
    }
    
    $data = $event['data'];
    
    // Extract required fields with validation
    $invoiceId = isset($data['metadata']['invoiceid']) ? $data['metadata']['invoiceid'] : null;
    $clientId = isset($data['metadata']['clientid']) ? $data['metadata']['clientid'] : null;
    $transactionId = isset($data['id']) ? $data['id'] : null;
    $status = isset($data['status']) ? $data['status'] : null;
    $amount = isset($data['amount']) ? ($data['amount'] / 100) : 0;
    $fee = isset($data['fees']) ? ($data['fees'] / 100) : 0;
    
    // Validate required fields
    if (empty($invoiceId) || empty($transactionId) || empty($status)) {
        throw new Exception('Missing required webhook data fields');
    }
    
    // Log all steps for debugging
    logTransaction($gatewayParams['name'], array(
        'invoice_id' => $invoiceId,
        'client_id' => $clientId,
        'transaction_id' => $transactionId,
        'status' => $status,
        'amount' => $amount,
        'fee' => $fee
    ), 'Webhook Data Extracted');
    
    // Validate callback invoice ID
    checkCbInvoiceID($invoiceId, $gatewayParams['name']);
    checkCbTransID($transactionId);
    
    if ($status === 'success') {
        $result = addInvoicePayment(
            $invoiceId,
            $transactionId,
            $amount,
            $fee,
            $gatewayModuleName
        );
        
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'add_payment_result' => $result
        ), 'Payment Added Successfully');
    } else {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status
        ), 'Payment Failed - Status Not Success');
    }

    // Return success response
    http_response_code(200);
    echo json_encode(array('status' => 'success', 'message' => 'Webhook processed'));
}

/**
 * Handle return from payment page
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleReturn($gatewayParams)
{
    global $CONFIG;
    
    // Get transaction reference from URL
    $reference = isset($_GET['reference']) ? $_GET['reference'] : '';
    
    if (empty($reference)) {
        throw new Exception('Missing transaction reference');
    }

    // Extract invoice ID from reference (support multiple formats)
    if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
        // New unique format: INV-123-1234567890-abcd1234
        $invoiceId = (int)$matches[1];
    } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
        // Simple format: INV-123
        $invoiceId = (int)$matches[1];
    } else {
        throw new Exception('Invalid transaction reference format: ' . $reference);
    }

    // Get system URL from WHMCS configuration
    $systemUrl = rtrim($CONFIG['SystemURL'], '/');
    if (empty($systemUrl)) {
        // Fallback to construct URL from server variables
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $systemUrl = $protocol . $_SERVER['HTTP_HOST'];
    }

    // Log the redirect for debugging
    logTransaction($gatewayParams['name'], array(
        'reference' => $reference,
        'invoice_id' => $invoiceId,
        'redirect_url' => $systemUrl . '/viewinvoice.php?id=' . $invoiceId
    ), 'Redirecting to Invoice');

    // Redirect to invoice
    header('Location: ' . $systemUrl . '/viewinvoice.php?id=' . $invoiceId);
    exit;
}