<?php
/**
 * Developed by WIDDX (https://widdx.com)
 * © 2025 All Rights Reserved
 * 
 * Lahza Payment Gateway Callback Handler for WHMCS
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';
require_once __DIR__ . '/../lahza/lib/LahzaApiHandler.php';

// Import Capsule for database access
use WHMCS\Database\Capsule;

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

// Initialize response
$response = array('status' => 'error', 'message' => 'Invalid request');

try {
    // Get request method
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    
    if ($requestMethod === 'POST') {
        // Handle webhook notification
        handleWebhook($gatewayParams);
    } elseif ($requestMethod === 'GET') {
        // Handle return from payment page
        handleReturn($gatewayParams);
    } else {
        throw new Exception('Invalid request method');
    }

} catch (Exception $e) {
    // Log error
    logTransaction($gatewayParams['name'], array('error' => $e->getMessage()), 'Callback Error: ' . $e->getMessage());
    
    // Return error response for webhooks
    if ($requestMethod === 'POST') {
        http_response_code(400);
        echo json_encode(array('status' => 'error', 'message' => $e->getMessage()));
    } else {
        // Redirect to invoice for GET requests
        if (isset($_GET['reference'])) {
            $reference = $_GET['reference'];
            $invoiceId = null;
            
            // Support multiple INV formats
            if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
                // New unique format: INV-123-1234567890-abcd1234
                $invoiceId = (int)$matches[1];
            } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
                // Simple format: INV-123
                $invoiceId = (int)$matches[1];
            } elseif (is_numeric($reference)) {
                // Pure numeric format (legacy)
                $invoiceId = (int)$reference;
            }
            
            if ($invoiceId) {
                global $CONFIG;
                $systemUrl = rtrim($CONFIG['SystemURL'], '/');
                if (empty($systemUrl)) {
                    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
                    $systemUrl = $protocol . $_SERVER['HTTP_HOST'];
                }
                header('Location: ' . $systemUrl . '/viewinvoice.php?id=' . $invoiceId);
                exit;
            }
        }
        die('Payment processing error. Please contact support.');
    }
}

/**
 * Handle webhook notifications from Lahza
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleWebhook($gatewayParams)
{
    global $gatewayModuleName;
    
    // Get raw POST data
    $rawPayload = file_get_contents('php://input');
    
    // Log raw payload for debugging
    logTransaction($gatewayParams['name'], array('raw_payload' => $rawPayload), 'Raw Webhook Payload Received');
    
    if (empty($rawPayload)) {
        throw new Exception('Empty webhook payload');
    }
    
    $event = json_decode($rawPayload, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON payload: ' . json_last_error_msg());
    }
    
    // Check webhook event type if available
    $eventType = isset($event['event']) ? $event['event'] : 'unknown';

    // Log parsed event for debugging
    logTransaction($gatewayParams['name'], array(
        'event_type' => $eventType,
        'event_data' => $event,
        'payload_size' => strlen($rawPayload)
    ), 'Parsed Webhook Event - Type: ' . $eventType);

    // Handle different webhook event types
    switch ($eventType) {
        case 'charge.success':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Successful Charge Event');
            break;
        case 'charge.failed':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Failed Charge Event');
            break;
        case 'refund.processed':
        case 'refund.failed':
        case 'refund.pending':
        case 'refund.processing':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Refund Event');
            // Handle refund events separately if needed
            break;
        default:
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Generic/Unknown Event');
            break;
    }
    
    // Get signature from headers - try multiple possible header names
    $signature = '';
    $possibleHeaders = array(
        'HTTP_X_LAHZA_SIGNATURE',
        'HTTP_X_SIGNATURE',
        'HTTP_LAHZA_SIGNATURE',
        'HTTP_SIGNATURE'
    );

    foreach ($possibleHeaders as $header) {
        if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
            $signature = $_SERVER[$header];
            break;
        }
    }

    // Log all headers for debugging
    logTransaction($gatewayParams['name'], array(
        'all_headers' => getallheaders(),
        'server_vars' => array_filter($_SERVER, function($key) {
            return strpos($key, 'HTTP_') === 0;
        }, ARRAY_FILTER_USE_KEY),
        'found_signature' => $signature
    ), 'Webhook Headers Analysis');

    if (empty($signature)) {
        // In test mode, we might skip signature verification
        if ($gatewayParams['testMode']) {
            logTransaction($gatewayParams['name'], array(
                'warning' => 'Missing webhook signature in test mode - proceeding anyway'
            ), 'Test Mode Signature Warning');
        } else {
            throw new Exception('Missing webhook signature');
        }
    } else {
        // Verify signature
        $calculatedSignature = hash_hmac('sha256', $rawPayload, $gatewayParams['secretKey']);

        if (!hash_equals($signature, $calculatedSignature)) {
            logTransaction($gatewayParams['name'], array(
                'received_signature' => $signature,
                'calculated_signature' => $calculatedSignature,
                'payload_length' => strlen($rawPayload),
                'secret_key_length' => strlen($gatewayParams['secretKey'])
            ), 'Signature Mismatch');
            throw new Exception('Invalid webhook signature');
        }
    }
    
    // Extract data from webhook with error checking - try multiple possible structures
    $data = null;
    $invoiceId = null;
    $clientId = null;
    $transactionId = null;
    $status = null;
    $amount = 0;
    $fee = 0;

    // Try different possible webhook structures
    if (isset($event['data']) && is_array($event['data'])) {
        $data = $event['data'];
    } elseif (isset($event['transaction']) && is_array($event['transaction'])) {
        $data = $event['transaction'];
    } elseif (is_array($event)) {
        $data = $event; // Sometimes the data is at the root level
    }

    if (!$data) {
        throw new Exception('Invalid webhook data structure - no data found');
    }

    // Extract required fields with multiple fallback options
    // Invoice ID
    if (isset($data['metadata']['invoiceid'])) {
        $invoiceId = $data['metadata']['invoiceid'];
    } elseif (isset($data['metadata']['invoice_id'])) {
        $invoiceId = $data['metadata']['invoice_id'];
    } elseif (isset($data['reference']) && preg_match('/INV-(\d+)/', $data['reference'], $matches)) {
        $invoiceId = $matches[1];
    }

    // Client ID
    if (isset($data['metadata']['clientid'])) {
        $clientId = $data['metadata']['clientid'];
    } elseif (isset($data['metadata']['client_id'])) {
        $clientId = $data['metadata']['client_id'];
    }

    // Transaction ID
    if (isset($data['id'])) {
        $transactionId = $data['id'];
    } elseif (isset($data['transaction_id'])) {
        $transactionId = $data['transaction_id'];
    } elseif (isset($data['reference'])) {
        $transactionId = $data['reference'];
    }

    // Status
    if (isset($data['status'])) {
        $status = strtolower($data['status']);
    } elseif (isset($data['state'])) {
        $status = strtolower($data['state']);
    }

    // Amount (convert from cents to main currency unit)
    if (isset($data['amount'])) {
        $amount = is_numeric($data['amount']) ? ($data['amount'] / 100) : 0;
    }

    // Fee (convert from cents to main currency unit)
    if (isset($data['fees'])) {
        $fee = is_numeric($data['fees']) ? ($data['fees'] / 100) : 0;
    } elseif (isset($data['fee'])) {
        $fee = is_numeric($data['fee']) ? ($data['fee'] / 100) : 0;
    }
    
    // Validate required fields
    if (empty($invoiceId) || empty($transactionId)) {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'error' => 'Missing required fields',
            'complete_data' => $data
        ), 'Webhook Validation Failed - Missing Required Fields');
        throw new Exception('Missing required webhook data fields: invoiceId or transactionId');
    }

    // Log all extracted data for debugging
    logTransaction($gatewayParams['name'], array(
        'invoice_id' => $invoiceId,
        'client_id' => $clientId,
        'transaction_id' => $transactionId,
        'status' => $status,
        'amount' => $amount,
        'fee' => $fee,
        'extraction_successful' => true
    ), 'Webhook Data Extracted Successfully');

    // Validate callback invoice ID
    try {
        checkCbInvoiceID($invoiceId, $gatewayParams['name']);
        logTransaction($gatewayParams['name'], array('invoice_id' => $invoiceId), 'Invoice ID Validation Passed');
    } catch (Exception $e) {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'error' => $e->getMessage()
        ), 'Invoice ID Validation Failed');
        throw $e;
    }

    // Check for duplicate transactions
    try {
        checkCbTransID($transactionId);
        logTransaction($gatewayParams['name'], array('transaction_id' => $transactionId), 'Transaction ID Validation Passed');
    } catch (Exception $e) {
        logTransaction($gatewayParams['name'], array(
            'transaction_id' => $transactionId,
            'error' => $e->getMessage()
        ), 'Transaction ID Validation Failed - Possible Duplicate');
        throw $e;
    }

    // Process payment based on status - Enhanced status handling
    $successStatuses = array('success', 'successful', 'completed', 'paid');
    $failedStatuses = array('failed', 'declined', 'cancelled', 'canceled', 'abandoned');
    $pendingStatuses = array('pending', 'processing', 'initiated', 'ongoing');

    if (in_array($status, $successStatuses)) {
        try {
            $result = addInvoicePayment(
                $invoiceId,
                $transactionId,
                $amount,
                $fee,
                $gatewayModuleName
            );

            logTransaction($gatewayParams['name'], array(
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'fee' => $fee,
                'add_payment_result' => $result,
                'status' => $status
            ), 'Payment Added Successfully');

        } catch (Exception $e) {
            logTransaction($gatewayParams['name'], array(
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ), 'Failed to Add Payment');
            throw $e;
        }
    } elseif (in_array($status, $failedStatuses)) {
        // Handle failed payments
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'action' => 'payment_failed'
        ), 'Payment Failed - Transaction Declined');

        // Optionally, you can add the failed transaction to WHMCS logs
        // This helps with tracking failed attempts

    } elseif (in_array($status, $pendingStatuses)) {
        // Handle pending payments
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'action' => 'payment_pending'
        ), 'Payment Pending - Awaiting Completion');

        // For pending payments, we don't mark as paid yet
        // We wait for a success webhook later

    } else {
        // Handle unknown status
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'all_statuses' => array(
                'success' => $successStatuses,
                'failed' => $failedStatuses,
                'pending' => $pendingStatuses
            )
        ), 'Payment Status Unknown - Unrecognized Status');
    }

    // Return success response
    http_response_code(200);
    echo json_encode(array('status' => 'success', 'message' => 'Webhook processed'));
}

/**
 * Handle return from payment page
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleReturn($gatewayParams)
{
    global $CONFIG;
    
    // Get transaction reference from URL
    $reference = isset($_GET['reference']) ? $_GET['reference'] : '';
    
    if (empty($reference)) {
        throw new Exception('Missing transaction reference');
    }

    // Extract invoice ID from reference (support multiple formats)
    if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
        // New unique format: INV-123-1234567890-abcd1234
        $invoiceId = (int)$matches[1];
    } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
        // Simple format: INV-123
        $invoiceId = (int)$matches[1];
    } else {
        throw new Exception('Invalid transaction reference format: ' . $reference);
    }

    // Get system URL from WHMCS configuration
    $systemUrl = rtrim($CONFIG['SystemURL'], '/');
    if (empty($systemUrl)) {
        // Fallback to construct URL from server variables
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $systemUrl = $protocol . $_SERVER['HTTP_HOST'];
    }

    // Log the redirect for debugging
    logTransaction($gatewayParams['name'], array(
        'reference' => $reference,
        'invoice_id' => $invoiceId,
        'redirect_url' => $systemUrl . '/viewinvoice.php?id=' . $invoiceId
    ), 'Redirecting to Invoice');

    // Redirect to invoice
    header('Location: ' . $systemUrl . '/viewinvoice.php?id=' . $invoiceId);
    exit;
}