# 📊 Lahza Payment Gateway - Payment Statuses Guide

## Overview

This document explains how the Lahza payment gateway handles different payment statuses and invoice states in WHMCS.

---

## 🔄 Payment Flow

```
Customer → Payment Page → Lahza Processing → Webhook → WHMCS → Invoice Update
```

1. **Customer initiates payment** on WHMCS invoice
2. **Redirected to Lahza** payment page
3. **Payment processed** by Lahza
4. **Webhook sent** to WHMCS callback
5. **Invoice status updated** based on payment result

---

## 📋 Payment Statuses

### ✅ Success Statuses

These statuses mark the invoice as **PAID** in WHMCS:

| Status | Description | Action |
|--------|-------------|---------|
| `success` | Payment completed successfully | ✅ Mark invoice as paid |
| `successful` | Alternative success status | ✅ Mark invoice as paid |
| `completed` | Payment completed | ✅ Mark invoice as paid |
| `paid` | Payment confirmed | ✅ Mark invoice as paid |

**WHMCS Action:** `addInvoicePayment()` is called to mark invoice as paid.

### ❌ Failed Statuses

These statuses indicate payment **FAILURE**:

| Status | Description | Action |
|--------|-------------|---------|
| `failed` | Payment failed | ❌ Log failure, no payment added |
| `declined` | Payment declined by bank | ❌ Log failure, no payment added |
| `cancelled` | Payment cancelled by user | ❌ Log failure, no payment added |
| `canceled` | Payment cancelled (US spelling) | ❌ Log failure, no payment added |
| `abandoned` | Payment abandoned by user | ❌ Log failure, no payment added |

**WHMCS Action:** No payment added, failure logged for tracking.

### ⏳ Pending Statuses

These statuses indicate payment is **IN PROGRESS**:

| Status | Description | Action |
|--------|-------------|---------|
| `pending` | Payment pending completion | ⏳ Wait for final status |
| `processing` | Payment being processed | ⏳ Wait for final status |
| `initiated` | Payment initiated | ⏳ Wait for final status |
| `ongoing` | Payment in progress | ⏳ Wait for final status |

**WHMCS Action:** No payment added yet, waiting for success/failure webhook.

---

## 🔔 Webhook Events

### Supported Lahza Webhook Events

| Event Type | Description | When Sent |
|------------|-------------|-----------|
| `charge.success` | Payment successful | When payment completes successfully |
| `charge.failed` | Payment failed | When payment fails or is declined |
| `refund.processed` | Refund completed | When refund is successfully processed |
| `refund.failed` | Refund failed | When refund cannot be processed |
| `refund.pending` | Refund pending | When refund is initiated |
| `refund.processing` | Refund processing | When refund is being processed |

### Webhook Payload Structure

```json
{
  "event": "charge.success",
  "data": {
    "id": "TXN_123456789",
    "status": "success",
    "amount": 5000,
    "currency": "USD",
    "reference": "INV-123-1234567890-abcd1234",
    "gateway_response": "Successful",
    "paid_at": "2025-01-16T10:30:00Z",
    "created_at": "2025-01-16T10:25:00Z",
    "channel": "card",
    "fees": 150,
    "metadata": {
      "invoiceid": "123",
      "clientid": "456",
      "description": "Web Hosting Service",
      "company": "Your Company"
    },
    "customer": {
      "id": 456,
      "email": "<EMAIL>",
      "phone": "+970591234567"
    }
  }
}
```

---

## 🔧 Implementation Details

### Status Processing Logic

```php
// Enhanced status handling in callback
$successStatuses = array('success', 'successful', 'completed', 'paid');
$failedStatuses = array('failed', 'declined', 'cancelled', 'canceled', 'abandoned');
$pendingStatuses = array('pending', 'processing', 'initiated', 'ongoing');

if (in_array($status, $successStatuses)) {
    // Mark invoice as paid
    addInvoicePayment($invoiceId, $transactionId, $amount, $fee, $gatewayName);
} elseif (in_array($status, $failedStatuses)) {
    // Log failure for tracking
    logTransaction($gatewayName, $data, 'Payment Failed');
} elseif (in_array($status, $pendingStatuses)) {
    // Wait for final status
    logTransaction($gatewayName, $data, 'Payment Pending');
} else {
    // Unknown status
    logTransaction($gatewayName, $data, 'Unknown Payment Status');
}
```

### Data Extraction

The webhook handler extracts data from multiple possible structures:

```php
// Try different webhook structures
if (isset($event['data'])) {
    $data = $event['data'];
} elseif (isset($event['transaction'])) {
    $data = $event['transaction'];
} else {
    $data = $event; // Root level data
}

// Extract invoice ID from multiple sources
if (isset($data['metadata']['invoiceid'])) {
    $invoiceId = $data['metadata']['invoiceid'];
} elseif (isset($data['reference']) && preg_match('/INV-(\d+)/', $data['reference'], $matches)) {
    $invoiceId = $matches[1];
}
```

---

## 🧪 Testing Payment Statuses

### Using the Test Tool

Access the webhook test tool:
```
https://yourdomain.com/modules/gateways/lahza/test_webhook.php?debug_key=lahza_debug_2025
```

### Test Scenarios

1. **Success Test:** Simulates successful payment
2. **Failed Test:** Simulates failed payment
3. **Pending Test:** Simulates pending payment

### Manual Testing

```bash
# Test webhook with curl
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -H "X-Lahza-Signature: YOUR_SIGNATURE" \
  -d '{"event":"charge.success","data":{"status":"success","id":"TEST123"}}'
```

---

## 📊 Monitoring and Logging

### WHMCS Gateway Logs

Check logs at: **Admin → Utilities → Logs → Gateway Log**

### Enhanced Debug Logs

View detailed logs:
```
https://yourdomain.com/modules/gateways/lahza/view_logs.php?debug_key=lahza_debug_2025
```

### Log Entries

- ✅ **Payment Added Successfully** - Success status processed
- ❌ **Payment Failed** - Failed status processed  
- ⏳ **Payment Pending** - Pending status processed
- ❓ **Payment Status Unknown** - Unrecognized status

---

## 🔍 Troubleshooting

### Common Issues

#### Invoice Not Marked as Paid
- **Check:** Payment status in webhook
- **Verify:** Invoice ID extraction
- **Confirm:** No duplicate transaction ID

#### Failed Payments Not Logged
- **Check:** Webhook signature verification
- **Verify:** Status mapping
- **Confirm:** Error handling

#### Pending Payments Stuck
- **Check:** If final webhook was sent
- **Verify:** Payment processor status
- **Confirm:** Manual verification needed

### Debug Steps

1. **Check Webhook Logs:**
   ```
   view_logs.php?debug_key=lahza_debug_2025
   ```

2. **Verify Webhook URL:**
   ```
   https://yourdomain.com/modules/gateways/callback/lahza.php
   ```

3. **Test Webhook Manually:**
   ```
   test_webhook.php?debug_key=lahza_debug_2025
   ```

4. **Check WHMCS Logs:**
   ```
   Admin → Utilities → Logs → Gateway Log
   ```

---

## 🔒 Security Considerations

### Webhook Signature Verification

```php
$signature = $_SERVER['HTTP_X_LAHZA_SIGNATURE'];
$calculatedSignature = hash_hmac('sha256', $payload, $secretKey);

if (!hash_equals($signature, $calculatedSignature)) {
    throw new Exception('Invalid webhook signature');
}
```

### IP Whitelisting

Whitelist Lahza IPs:
- `*************`
- `**************`

### Duplicate Prevention

```php
checkCbTransID($transactionId); // Prevents duplicate processing
```

---

## 📚 References

- **Lahza API Docs:** https://docs.lahza.io/
- **WHMCS Gateway Docs:** https://developers.whmcs.com/payment-gateways/
- **Webhook Testing:** test_webhook.php
- **Log Monitoring:** view_logs.php

---

**💡 Pro Tip:** Always test payment statuses in test mode before going live to ensure proper invoice handling.
